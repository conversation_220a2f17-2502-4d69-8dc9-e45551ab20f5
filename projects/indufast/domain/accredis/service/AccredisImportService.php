<?php

  namespace domain\accredis\service;

  use AppModel;
  use DateTime;
  use Exception;
  use GsdDbException;
  use GsdException;
  use IndufastEmployee;
  use IndufastWorkday;
  use IndufastWorkdayLine;
  use Setting;

  class AccredisImportService {

    public const string LAST_ACCREDIS_IMPORT_CODE = 'last_accredis_import';
    public const string LAST_ACCREDIS_IMPORT_RESULT_CODE = 'last_accredis_import_result';

    /**
     * @throws GsdDbException
     * @throws GsdException
     * @throws Exception
     */
    public function checkUploadedFiles(): array {
      $results = [];

      foreach (glob(DIR_ACCREDIS_UPLOAD . "*.xml") as $filename) {
        try {
          $trips = $this->parseAccredisXMLUpload($filename);
          $results[] = $this->saveTrips($trips, $filename);
          unlink($filename);
        } catch (Exception $e) {
          $results[] = [
            'filename' => basename($filename),
            'time' => date('Y-m-d H:i:s'),
            'warnings' => [],
            'errors' => [$e->getMessage()],
            'messages' => [],
          ];
        }
      }

      if ($results) {
        $this->logResults($results);
      }

      return $results;
    }

    public function logResults(array $results): void {
      if (!$setting = Setting::find_by(['code' => self::LAST_ACCREDIS_IMPORT_RESULT_CODE])) {
        $setting = new Setting();
        $setting->code = self::LAST_ACCREDIS_IMPORT_RESULT_CODE;
      }
      $setting->value = json_encode($results);
      $setting->save();
      if (!$setting = Setting::find_by(['code' => self::LAST_ACCREDIS_IMPORT_CODE])) {
        $setting = new Setting();
        $setting->code = self::LAST_ACCREDIS_IMPORT_CODE;
      }
      $setting->value = date('Y-m-d H:i:s');
      $setting->save();
    }

    /**
     * Parse the XML and return an array grouped by driver and date.
     *
     * @throws Exception
     */
    public function parseAccredisXMLUpload(string $filename): array {
      $trips = [];

      if (!$xml = @simplexml_load_file($filename)) {
        throw new Exception('Kan XML niet parsen.');
      }

      // Gather all trips.
      foreach ($xml->xpath('/TripList/Trip') as $trip) {
        $start = new DateTime((string)$trip->Start);
        $end = new DateTime((string)$trip->End);

        $workdayLine = new IndufastWorkdayLine();
        $workdayLine->external_id = (int)$trip->Number;
        $workdayLine->start = $start->format('H:i:s');
        $workdayLine->end = $end->format('H:i:s');
        $workdayLine->distance = (float)$trip->Distance;
        $workdayLine->vehicle = (string)$trip->Vehicle;
        $workdayLine->start_address = implode(', ', array_filter([
          (string)$trip->FromStreet,
          (string)$trip->FromPostcode,
          (string)$trip->FromCity,
          (string)$trip->FromCountry,
        ]));
        $workdayLine->end_address = implode(', ', array_filter([
          (string)$trip->ToStreet,
          (string)$trip->ToPostcode,
          (string)$trip->ToCity,
          (string)$trip->ToCountry,
        ]));

        $trips[(string)$trip->Driver][(string)$trip->Date][] = $workdayLine;
      }

      if (!$trips) {
        throw new Exception('Geen ritten gevonden in het bestand.');
      }

      return $trips;
    }

    /**
     * Store the workdays and related workday lines into the database.
     *
     * @throws GsdDbException
     * @throws GsdException
     */
    public function saveTrips(array $trips, string $filename): array {
      $drivers = AppModel::mapObjectIds(IndufastEmployee::find_all(' WHERE name_accredis IS NOT NULL'), 'name_accredis');
      $workdaySkipped = 0;
      $workdayInsert = 0;
      $workdayLineSkipped = 0;
      $workdayLineSkippedUnknownDriver = 0;
      $unknownDrivers = 0;
      $workdayLineInsert = 0;
      $result = [
        'filename' => basename($filename),
        'time' => date('Y-m-d H:i:s'),
        'warnings' => [],
        'errors' => [],
        'messages' => [],
      ];

      foreach ($trips as $driver => $section) {
        if (!isset($drivers[$driver])) {
          $result['warnings'][] = sprintf('Onbekende chauffeur: %s', $driver);
          $unknownDrivers++;
          $workdayLineSkippedUnknownDriver += count($section);
          continue;
        }

        /** @var IndufastWorkdayLine[] $workdayLines */
        foreach ($section as $date => $workdayLines) {

          // Load or create the workday.
          if (!$workday = IndufastWorkday::find_by(['date' => $date, 'employee_id' => $drivers[$driver]->id])) {
            $workday = new IndufastWorkday();
            $workday->date = $date;
            $workday->employee_id = $drivers[$driver]->id;
            if ($workday->save($result['errors'])) {
              $workdayInsert++;
            }
          }
          else {
            $workdaySkipped++;
          }

          foreach ($workdayLines as $workdayLine) {
            $workdayLine->workday_id = $workday->id;

            // Create a new workday line?
            if (!IndufastWorkdayLine::find_by(['workday_id' => $workday->id, 'start' => $workdayLine->start, 'end' => $workdayLine->end])) {
              if ($workdayLine->save($result['errors'])) {
                $workdayLineInsert++;
              }
            }
            else {
              $workdayLineSkipped++;
            }
          }
        }
      }

      $result['messages'][] = sprintf('%d werkdagen aangemaakt en %d ritten toegevoegd.', $workdayInsert, $workdayLineInsert);
      $result['messages'][] = sprintf('%d werkdagen en %d ritten overgeslagen omdat ze al bestaan.', $workdaySkipped, $workdayLineSkipped);
      $result['messages'][] = sprintf('%d ritten overgeslagen omdat %d chauffeurs niet bestaan.', $workdayLineSkippedUnknownDriver, $unknownDrivers);

      return $result;
    }

  }