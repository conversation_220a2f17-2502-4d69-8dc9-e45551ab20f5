<script setup>
import {computed, onMounted, ref, watch} from "vue";
import {useRouter, useRoute} from "vue-router";
import {useTitle} from "vue-page-title";
import createApiService from "@/services/api.js";
import {definePage} from "unplugin-vue-router/runtime";
import {eachDayOfInterval, format, getDay, getWeek} from "date-fns";
import { vMaska } from "maska/vue"
import {holidayDisplayNames} from "@/helpers/constants.js";
import WorkdayEditDialog from "@/components/time_registration/WorkdayEditDialog.vue";
import SpecialHoursEditDialog from "@/components/time_registration/SpecialHoursEditDialog.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";
import {timeInMinutes} from "@/helpers/helpers.js";
import {useUnsavedChangesPrompt} from "@/composables/useUnsavedChangesPrompt.js";
import {translateErrors} from "@/helpers/translateErrors.js";
import {useSnackbarStore} from "@/stores/snackbar.js";

const router = useRouter();
const api = createApiService(router);
const {setTitle} = useTitle();
const route = useRoute();
const summary = ref();
const workdays = ref([]);
const projects = ref();
const changedWorkdays = ref({});
const loading = ref({});
const user = ref();
const openDays = ref([]);
const newLine = ref({
  begin: '',
  end: '',
});
const holidays = ref();
const errors = ref({});
const snackbarStore = useSnackbarStore();

const hasUnsavedChanges = computed(() => !!Object.keys(changedWorkdays.value).length);
useUnsavedChangesPrompt(hasUnsavedChanges);

definePage({
  name: "time-registration-details",
  meta: {
    title: 'Tijdsregistratie',
    requiresAuth: true,
  },
})

onMounted(() => {
  refreshWorkdays();
  refreshProjects();
});

const latestUsedParams = ref();
const refreshWorkdays = async () => {
  changedWorkdays.value = {}

  try {
    latestUsedParams.value = {
      employee_id: route.params.employee,
      year: route.params.year,
      month: route.params.month,
    };
    const workdayParams = new URLSearchParams(latestUsedParams.value).toString();

    const [holidaysResponse, employeeResponse, workdaysResponse] = await Promise.all([
      holidays.value ?? api.get("getHolidays"),
      user.value ?? api.get(`employeeGet?id=${route.params.employee}`),
      api.get(`workdayList?${workdayParams}`)
    ]);

    holidays.value ??= holidaysResponse.data.data;
    user.value ??= employeeResponse.data.data;
    setTitle(user.value.name);

    workdays.value = [];
    openDays.value = [];
    eachDayOfInterval({
      start: new Date(route.params.year, route.params.month - 1, 1),
      end: new Date(route.params.year, route.params.month, 0),
    }).forEach((date) => {
      const workday = getWorkday(date, workdaysResponse.data.data);
      workdays.value.push(workday);
      if (workday.status === 'new' && workday.id) {
        openDays.value.push(workdays.value.length - 1);
      }
    });

    refreshWorkdaySummary();
  } catch (error) {
    console.log(error);
  }
}

const refreshSpecialHours = async (workday) => {
  try {
    const response = await api.get(`workdaySpecialHoursList?workday_id=${workday.id}`);

    const workdayLocal = workdays.value.find(w => w.id === workday.id);
    if (!workdayLocal) return;

    workdayLocal.specialHours = response.data.data;
    await updateCalculation(workdayLocal);
  } catch (error) {
    console.log(error);
  }
}

const refreshProjects = async () => {
  const params = new URLSearchParams({
    employee_id: route.params.employee,
    year: route.params.year,
    month: route.params.month,
  }).toString();

  api.get(`projectList?${params}`)
    .then((response) => {
      projects.value = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });
}

const getProjectsOnDate = (date) => {
  if (!projects.value) {
    return [];
  }

  return projects.value.filter((project) => {
    return getEventFromProject(project, date);
  });
}

const getEventFromProject = (project, date) => {
  return project.events.find((event) => {
    return format(event.start, 'yyyy-MM-dd') === date && event.employee_ids.includes(parseInt(route.params.employee));
  });
}

const getWorkday = (date, workdays) => {
  const workday = workdays.find((workday) => workday.date === format(date, 'yyyy-MM-dd'));
  if (workday) {
    return workday;
  }
  else {
    return {
      id: null,
      date: format(date, 'yyyy-MM-dd'),
      status: 'new',
      lines: [],
      travel_to_end_line_id: null,
      travel_from_start_line_id: null,
      remark: '',
    };
  }
}

const refreshWorkdaySummary = () => {
  api.get("workdaySummary?" + new URLSearchParams({
    employee_id: route.params.employee,
    year: route.params.year,
    month: route.params.month,
  }).toString())
    .then((response) => {
      summary.value = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });
}

const updateCalculation = async (workday) => {
  let data = JSON.parse(JSON.stringify(workday));
  data.workdayLinesData = {};
  data.lines.forEach((line) => {
    data.workdayLinesData[line.id] = {
      void: line.void,
    };
  });

  loading.value[workday.date] = true;
  try {
    const response = await api.post("workdayCalculate?id=" + workday.id, JSON.stringify(data))
    const workdayIndex = workdays.value.findIndex((workdayLocal) => workdayLocal.id === workday.id);
    workdays.value[workdayIndex] = response.data.data;
  } catch(error) {
    console.log(error);
  } finally {
    loading.value[workday.date] = false;
  }
}

const saveWorkday = async (workday) => {
  loading.value[workday.date] = true;

  try {
    if (workday.specialHours) {
      const deletionPromises = workday.specialHours
        .filter(specialHours => specialHours.markedForDeletion && specialHours.id)
        .map(specialHours => api.delete(`workdaySpecialHoursDelete?id=${specialHours.id}`));

      if (deletionPromises.length > 0) {
        await Promise.all(deletionPromises);
      }
    }

    let workdayLinesData = {};
    workday.lines.forEach((line) => {
      workdayLinesData[line.id] = {
        void: line.void,
      };
    });

    let json = JSON.stringify({
      employee_id: route.params.employee,
      date: workday.date,
      travel_to_end_line_id: workday.travel_to_end_line_id,
      travel_from_start_line_id: workday.travel_from_start_line_id,
      workdayLinesData: workdayLinesData,
      remark: workday.remark,
      status: workday.status,
      break_time: workday.break_time,
      work_time: workday.work_time,
    });

    const response = await api.post((workday.id) ? "workdayUpdate?id=" + workday.id : "workdayCreate", json);

    const workdayIndex = workdays.value.findIndex((workdayLocal) => workdayLocal.date === workday.date);
    workdays.value[workdayIndex] = response.data.data;
    delete changedWorkdays.value[workday.date];
    if (workdays.value[workdayIndex].status === 'processed') {
      openDays.value = openDays.value.filter((day) => day !== workdayIndex);
    }
    refreshWorkdaySummary();
    loading.value[workday.date] = false;
  } catch (error) {
    loading.value[workday.date] = false;
    console.log(error);
  }
}

const updateWorkday = (workday, newWorkday) => {
  const workdayIndex = workdays.value.findIndex((workdayLocal) => workdayLocal.id === workday.id);

  // Preserve some data of the original workday.
  newWorkday.status = workday.status;
  newWorkday.remark = workday.remark;

  workdays.value[workdayIndex] = newWorkday;
  delete changedWorkdays.value[workday.date];
  if (workdays.value[workdayIndex].status === 'processed') {
    openDays.value = openDays.value.filter((day) => day !== workdayIndex);
  }
  refreshWorkdaySummary()
}

const saveWorkdayLine = async (workday, isActive) => {
  loading.value[workday.date] = true;
  try {
    const json = JSON.stringify({
      start: newLine.value.start,
      end: newLine.value.end,
      workday_id: workday.id,
    });

    const response = await api.post("workdayLineCreate", json);
    updateWorkday(workday, response.data.data);

    newLine.value = {};
    isActive.value = false;
  } catch (error) {
    snackbarStore.showMessage("Er is een fout opgetreden bij het opslaan!", "error");
    errors.value = error.response?.data?.data;
  } finally {
    loading.value[workday.date] = false;
  }
};

const markSpecialHoursForDeletion = (workday, specialHours) => {
  specialHours.markedForDeletion = !specialHours.markedForDeletion;
  changedWorkdays.value[workday.date] = true;
};

const saveAllWorkdays = () => {
  for (const workdayDate in changedWorkdays.value) {
    saveWorkday(workdays.value.find((workday) => workday.date === workdayDate));
  }
};

const setTravelTo = (item) => {
  const workday = workdays.value.find((workday) => workday.id === item.workday_id);
  workday.travel_to_end_line_id = (workday.travel_to_end_line_id !== item.id) ? item.id : null;
  changedWorkdays.value[workday.date] = true;
  updateCalculation(workday);
};

const setTravelFrom = (item) => {
  const workday = workdays.value.find((workday) => workday.id === item.workday_id);
  workday.travel_from_start_line_id = (workday.travel_from_start_line_id !== item.id) ? item.id : null;
  changedWorkdays.value[workday.date] = true;
  updateCalculation(workday);
};

const voidLine = (item) => {
  item.void = !item.void;

  const workday = workdays.value.find((workday) => workday.id === item.workday_id);

  changedWorkdays.value[workday.date] = true;
  updateCalculation(workday);
};

const isNonWorkingDay = (date) => {
  const isEvenWeek = getWeek(date, {weekStartsOn: 1}) % 2 === 0;
  const nonWorkingDays = isEvenWeek ? user.value.non_working_days_even_weeks ?? [] : user.value.non_working_days_uneven_weeks ?? [];

  return nonWorkingDays.indexOf(format(date, 'eeee').toLowerCase()) !== -1;
}

const getIcon = (workday, color = false) => {
  if (!workday.id) {
    return (color) ? 'grey' : 'mdi-plus';
  }

  if (changedWorkdays.value[workday.date]) {
    return (color) ? 'indufastRed' : 'mdi-content-save';
  }

  if (workday.status === 'processed') {
    return (color) ? 'indufastGreen' : 'mdi-check';
  }

  return (color) ? 'text-black' : 'mdi-alert-box-outline';
}

const getClasses = (workday) => {
  let classes = [];
  if (new Date(workday.date).getDay() === 0 || new Date(workday.date).getDay() === 6) {
    classes.push('weekend');
  }

  if (isHoliday(workday.date)) {
    classes.push('holiday-workday');
  }

  if (!workday.id) {
    classes.push('new');
  }

  return classes;
};

const rowProps = (row) => {
  return {
    class: {
      "workday-line": true,
      "void": row.item.void,
      "travel-from": !row.item.void && row.item.type === 'travel-from',
      "travel-to": !row.item.void && row.item.type === 'travel-to',
    },
  };
};

const specialHoursRowProps = (row) => {
  return {
    class: {
      "special-hours-line": true,
      "marked-for-deletion": row.item.markedForDeletion,
    },
  };
};

const dayHeaders = [
  {title: "Start", value: "start", cellProps: {class: "no-wrap"}},
  {title: "Eind", value: "end", cellProps: {class: "no-wrap"}},
  {title: "Duur", value: "duration", cellProps: {class: "no-wrap"}},
  {title: "Voertuig", value: "vehicle", cellProps: {class: "no-wrap"}},
  {title: "Van", value: "start_address"},
  {title: "Naar", value: "end_address"},
  {title: "Afstand", value: "distance", cellProps: {class: "no-wrap"}},
  {title: "Acties", value: "actions", cellProps: {class: "no-wrap"}},
];

const specialHoursHeaders = [
  {title: "Type", value: "type"},
  {title: "Duur", value: "duration", cellProps: {class: "no-wrap"}},
  {title: "Acties", value: "actions", cellProps: {class: "no-wrap"}, align: "end"},
];

const specialHoursTypes = [
  {value: 'leave', title: 'Verlof'},
  {value: 'special-leave', title: 'Bijzonder verlof'},
  {value: 'sick', title: 'Ziek'},
  {value: 'unexcused-leave', title: 'Ongeoorloofd afwezig'},
];

const getSpecialHoursTypeLabel = (type) => {
  const typeObj = specialHoursTypes.find(t => t.value === type);
  return typeObj ? typeObj.title : type;
};

const getDaySummary = (day) => {
  let summary = [];

  if (day.workdayDurationNet) {
    summary.push('Uren gewerkt: ' + day.workdayDurationNet);
  }
  if (day.travelDurationNet && day.travelDurationNet !== '00:00:00') {
    summary.push('Reistijd: ' + day.travelDurationNet);
  }
  if (day.specialHoursLeave) {
    summary.push('Verlof: ' + day.specialHoursLeave);
  }
  if (day.specialHoursSpecialLeave) {
    summary.push('Bijzonder verlof: ' + day.specialHoursSpecialLeave);
  }
  if (day.specialHoursSick) {
    summary.push('Ziek: ' + day.specialHoursSick);
  }
  if (day.specialHoursUnexcusedLeave) {
    summary.push('Ongeoorloofd afwezig: ' + day.specialHoursUnexcusedLeave);
  }

  return summary.join(' - ');
}

const workdaySummary = computed(() => {
  return [
    [
      {title: 'Aantal afgeronde dagen', value: summary.value?.count},
      {title: 'Aantal open dagen', value: openDayCount},
      {title: 'Totaal gewerkte uren', value: summary.value?.total},
      {title: 'Uren per maand', value: summary.value?.hoursPerMonth},
      {title: 'Maandelijkse saldo', value: summary.value?.monthlyBalance},
      {title: 'Totaal saldo', value: summary.value?.totalBalance},
    ],
    [
      {title: 'Uren op zaterdag', value: summary.value?.totalSaturday},
      {title: 'Uren op zondag of feestdag', value: summary.value?.totalSundayOrHoliday},
      {title: 'Overuren < 3:00', value: summary.value?.totalOvertimeBelow},
      {title: 'Overuren > 3:00', value: summary.value?.totalOvertimeAbove},
      {title: 'Verschoven uren', value: summary.value?.totalOutsideWorkingHours},
      {title: 'Reistijd', value: summary.value?.totalTravel},
    ],
    [
      {title: 'Verlof', value: summary.value?.totalLeave},
      {title: 'Bijzonder verlof', value: summary.value?.totalSpecialLeave},
      {title: 'Ziekte', value: summary.value?.totalSick},
      {title: 'Ongeoorloofd afwezig', value: summary.value?.totalUnexcusedLeave},
    ],
  ];
});

watch(route, () => {
  if (route.params.month !== latestUsedParams.value.month) {
    if (hasUnsavedChanges.value) {
      if (!confirm('Er zijn nog wijzigingen die niet zijn opgeslagen. Weet je zeker dat je de pagina wil verlaten?')) {
        router.replace({
          name: 'time-registration-details',
          params: {
            employee: latestUsedParams.value.employee_id,
            year: latestUsedParams.value.year,
            month: latestUsedParams.value.month,
          }
        });
        return;
      }
    }
    refreshWorkdays();
    refreshProjects();
  }
})

const isHoliday = (date) => {
  return Object.keys(holidays.value).includes(date);
}
const getHolidayName = (date) => {
  const holiday = holidays.value[date];
  return holidayDisplayNames[holiday] ?? holiday;
}

const editDialog = ref(false);
const editingWorkday = ref(null);
const openEditDialog = (workday) => {
  editingWorkday.value = workday;
  editDialog.value = true;
};

const specialHoursDialog = ref(false);
const editingSpecialHours = ref(null);
const editingSpecialHoursWorkday = ref(null);
const openSpecialHoursDialog = (workday, specialHours = null) => {
  editingSpecialHoursWorkday.value = workday;
  editingSpecialHours.value = specialHours;
  specialHoursDialog.value = true;
};

const lockingMonth = ref(false);
const showLockConfirm = ref(false);

const lockMonth = () => {
  if (!summary.value) return snackbarStore.showMessage("Geen samenvatting beschikbaar om de maand af te sluiten", "error");
  if (openDays.value.length) return snackbarStore.showMessage("Er zijn nog open dagen. Sluit deze eerst af.", "error");

  showLockConfirm.value = true;
};

const confirmLockMonth = async () => {
  showLockConfirm.value = false;
  lockingMonth.value = true;

  try {
    summary.value.locked = true;
    const lockData = {
      employee_id: route.params.employee,
      year: route.params.year,
      month: route.params.month,
      data: summary.value
    };
    await api.post('workdaySummaryLock', JSON.stringify(lockData));
    snackbarStore.showMessage("Maand succesvol afgesloten", "success");
    await refreshWorkdays();
  } catch (error) {
    console.log(error);
    snackbarStore.showMessage("Er is een fout opgetreden bij het afsluiten van de maand", "error");
  } finally {
    lockingMonth.value = false;
  }
};

const cancelLockMonth = () => {
  showLockConfirm.value = false;
};

const openDayCount = computed(() => {
  return workdays.value.filter((day) => day.status === 'new' && day.id).length;
});
</script>

<template>
  <v-card>
    <v-card-title class="mt-2 space-between align-center ga-3">
      {{ user?.name ?? 'Tijdsregistratie' }}
      <v-spacer />
      <v-icon
        v-if="user?.id"
        icon="mdi-refresh"
        size="small"
        color="primary"
        @click="refreshWorkdays(); refreshProjects()"
      />
      <v-btn
        color="warning"
        variant="elevated"
        prepend-icon="mdi-lock"
        :loading="lockingMonth"
        :disabled="summary?.locked || openDayCount !== 0"
        @click="lockMonth"
      >
        {{ summary?.locked ? 'Maand afgesloten' : 'Maand afsluiten' }}
      </v-btn>
    </v-card-title>
    <v-card-subtitle v-if="user && user.name_accredis && user.name!== user.name_accredis">
      Accredis: {{ user.name_accredis }}
    </v-card-subtitle>
  </v-card>
  <v-divider />
  <v-container
    max-width="1400"
    class="space-between ga-4 overflow-x-auto"
  >
    <v-table
      v-for="(column, index) in workdaySummary"
      :key="index"
      class="summary totalSummary"
    >
      <tr
        v-for="(item, itemIndex) in column"
        :key="itemIndex"
      >
        <th class="no-wrap">
          {{ item.title }}
        </th>
        <td>{{ item.value }}</td>
      </tr>
    </v-table>
  </v-container>
  <v-divider />
  <v-expansion-panels
    v-model="openDays"
    multiple
    elevation="0"
  >
    <v-expansion-panel
      v-for="(day, index) in workdays"
      :key="day.id"
    >
      <v-expansion-panel-title
        :class="getClasses(day)"
      >
        <v-icon
          class="mr-1"
          :color="getIcon(day, true)"
        >
          {{ getIcon(day) }}
        </v-icon>
        {{ $filters.ucFirst($filters.formatDate(day.date)) }}
        <v-icon
          v-if="isNonWorkingDay(day.date)"
          icon="mdi-briefcase-variant-off-outline"
          color="indufastPurple"
          title="Deze medewerker is roostervrij op deze datum"
          class="ml-3"
        />
        <v-chip
          v-if="getDay(day.date) === 1"
          color="grey"
          class="ml-3"
          variant="outlined"
        >
          {{ 'Week ' + getWeek(day.date, {weekStartsOn: 1}) }}
        </v-chip>
        <v-chip
          v-if="isHoliday(day.date)"
          color="indufastGreen"
          class="ml-3"
          variant="outlined"
        >
          {{ getHolidayName(day.date) }}
        </v-chip>
        <v-icon
          v-if="day.id && openDays.indexOf(index) !== -1"
          icon="mdi-pencil"
          class="ml-3"
          :disabled="day.status !== 'new'"
          @click.stop="openEditDialog(day)"
        />
        <v-spacer />
        <span v-if="day.status !== 'new' && openDays.indexOf(index) === -1">
          {{ getDaySummary(day) }}
        </span>
      </v-expansion-panel-title>
      <v-expansion-panel-text class="pa-6">
        <div class="summary">
          <template
            v-for="project in getProjectsOnDate(day.date)"
            :key="project.id"
          >
            <h4>
              Project
            </h4>
            <v-table>
              <tr>
                <th>Naam</th>
                <td class="wrap">{{ project.name }}</td>
              </tr>
              <tr>
                <th>Adres</th>
                <td class="wrap">{{ project.address }}</td>
              </tr>
              <tr>
                <th>Planning</th>
                <td>
                  {{ $filters.formatTime(getEventFromProject(project, day.date).start) }} - {{ $filters.formatTime(getEventFromProject(project, day.date).end) }}
                </td>
              </tr>
            </v-table>
          </template>

          <h4 v-if="day.workdayDuration">
            Werkdag
          </h4>
          <v-table v-if="day.workdayDuration">
            <tr v-if="!day.work_time">
              <th>Werktijd</th>
              <td>{{ day.workdayStart }} - {{ day.workdayEnd }}</td>
            </tr>
            <tr>
              <td>Bruto</td>
              <td>
                <span :class="{'overriden': day.work_time}">
                  {{ day.workdayDuration }}
                </span>
              </td>
            </tr>
            <tr>
              <th>Pauze</th>
              <td>
                <span :class="{'overriden': day.break_time}">
                  {{ timeInMinutes(day.break_time || day.workdayPause) }} minuten
                </span>
              </td>
            </tr>
            <tr v-if="day.overtimeBelow">
              <th>Overuren &lt; 3:00</th>
              <td>{{ day.overtimeBelow }}</td>
            </tr>
            <tr v-if="day.overtimeAbove">
              <th>Overuren &gt; 3:00</th>
              <td>{{ day.overtimeAbove }}</td>
            </tr>
            <tr class="netDuration">
              <th>Netto</th>
              <td>
                {{ day.workdayDurationNetClipped }}
              </td>
            </tr>
            <tr class="netDuration" v-if="day.workdayOutsideWorkingHours">
              <th>Verschoven</th>
              <td>{{ day.workdayOutsideWorkingHours }}</td>
            </tr>
          </v-table>

          <template v-if="day.travel_to_end_line_id || day.travel_from_start_line_id">
            <h4>Reistijd</h4>
            <v-table>
              <template v-if="day.travel_to_end_line_id">
                <tr>
                  <th>Heenreis</th>
                  <td>{{ day.travelToStart }} - {{ day.travelToEnd }}</td>
                </tr>
                <tr>
                  <th>Afstand</th>
                  <td>{{ $filters.formatDistance(day.travelToDistance) }}</td>
                </tr>
                <tr>
                  <th>Bruto</th>
                  <td>{{ day.travelToDuration }}</td>
                </tr>
                <tr class="netDuration">
                  <th>Netto</th>
                  <td>{{ day.travelToDurationNet }}</td>
                </tr>
              </template>

              <template v-if="day.travel_from_start_line_id">
                <tr>
                  <th>Terugreis</th>
                  <td>{{ day.travelFromStart }} - {{ day.travelFromEnd }}</td>
                </tr>
                <tr>
                  <th>Afstand</th>
                  <td>{{ $filters.formatDistance(day.travelFromDistance) }}</td>
                </tr>
                <tr>
                  <th>Bruto</th>
                  <td>{{ day.travelFromDuration }}</td>
                </tr>
                <tr class="netDuration">
                  <th>Netto</th>
                  <td>{{ day.travelFromDurationNet }}</td>
                </tr>
              </template>

              <tr class="totalDuration">
                <th>Totaal</th>
                <td>{{ day.travelDurationNet }}</td>
              </tr>
            </v-table>
          </template>

          <template v-if="day.specialHoursDuration">
            <h4>Bijzondere uren</h4>
            <v-table>
              <tr v-if="day.specialHoursLeave">
                <th>Verlof</th>
                <td>{{ day.specialHoursLeave }}</td>
              </tr>
              <tr v-if="day.specialHoursSpecialLeave">
                <th>Bijzonder verlof</th>
                <td>{{ day.specialHoursSpecialLeave }}</td>
              </tr>
              <tr v-if="day.specialHoursSick">
                <th>Ziek</th>
                <td>{{ day.specialHoursSick }}</td>
              </tr>
              <tr class="totalDuration">
                <th>Totaal</th>
                <td>{{ day.specialHoursDuration }}</td>
              </tr>
            </v-table>
          </template>
        </div>
        <div class="lines">
          <h4>Ritten</h4>
          <v-data-table
            :items="day.lines"
            :headers="dayHeaders"
            item-key="id"
            :row-props="rowProps"
            density="compact"
            :loading="loading[day.date]"
            class="rowHover"
            :items-per-page="-1"
            hide-default-footer
          >
            <template #item.distance="{ value }">
              {{ value ? $filters.formatDistance(value) : '' }}
            </template>

            <template #item.actions="{ item }">
              <v-icon
                :disabled="!item.canBeSetAsTravelToEnd || day.status !== 'new'"
                color="indufastGreen"
                title="Heenreis instellen"
                @click="setTravelTo(item)"
              >
                mdi-arrow-collapse-down
              </v-icon>
              <v-icon
                :disabled="!item.canBeSetAsTravelFromStart || day.status !== 'new'"
                color="indufastCyan"
                title="Terugreis instellen"
                @click="setTravelFrom(item)"
              >
                mdi-arrow-collapse-up
              </v-icon>
              <v-icon
                :disabled="!item.canBeVoid || day.status !== 'new'"
                color="indufastRed"
                :title="item.void ? 'Regel inschakelen' : 'Regel uitschakelen'"
                @click="voidLine(item)"
              >
                {{ item.void ? 'mdi-undo' : 'mdi-close' }}
              </v-icon>
            </template>
          </v-data-table>

          <v-divider v-if="day.specialHours && day.specialHours.length > 0" class="my-4" />
          <h4 v-if="day.specialHours && day.specialHours.length > 0" class="mb-3">Bijzondere uren</h4>
          <v-data-table
            v-if="day.specialHours && day.specialHours.length > 0"
            :items="day.specialHours"
            :headers="specialHoursHeaders"
            item-key="id"
            :row-props="specialHoursRowProps"
            density="compact"
            :loading="loading[day.date]"
            class="rowHover mb-4"
            :items-per-page="-1"
            hide-default-footer
          >
            <template #item.type="{ value }">
              {{ getSpecialHoursTypeLabel(value) }}
            </template>

            <template #item.actions="{ item }">
              <v-icon
                :disabled="day.status !== 'new' || item.markedForDeletion"
                color="primary"
                title="Bijzondere uren bewerken"
                @click="openSpecialHoursDialog(day, item)"
              >
                mdi-pencil
              </v-icon>
              <v-icon
                :disabled="day.status !== 'new'"
                color="indufastRed"
                :title="item.markedForDeletion ? 'Bijzondere uren inschakelen' : 'Bijzondere uren uitschakelen'"
                @click="markSpecialHoursForDeletion(day, item)"
              >
                {{ item.markedForDeletion ? 'mdi-undo' : 'mdi-close' }}
              </v-icon>
            </template>
          </v-data-table>
          <div>
            <v-textarea
              v-model="day.remark"
              label="Opmerkingen"
              variant="outlined"
              :disabled="day.status !== 'new'"
              rows="1"
              auto-grow
              hide-details
              class="mb-2 mt-6"
              @keydown="changedWorkdays[day.date] = true"
            />
          </div>
        </div>
        <v-card-actions v-if="!summary?.locked">
          <v-spacer />
          <v-dialog max-width="500">
            <template #activator="{ props: activatorProps }">
              <v-btn
                v-bind="activatorProps"
                prepend-icon="mdi-plus"
                :disabled="day.status !== 'new' || !day.id"
              >
                Regel
              </v-btn>
            </template>
            <template #default="{ isActive }">
              <v-card>
                <v-card-title class="bg-primary">
                  Regel toevoegen op {{ $filters.formatDate(day.date) }}
                </v-card-title>
                <v-card-text>
                  <v-text-field
                    v-model="newLine.start"
                    v-maska="'##:##'"
                    placeholder="07:00"
                    label="Van"
                    prepend-icon="mdi-clock-time-four-outline"
                    :error-messages="translateErrors(errors.start, 'Van')"
                  />
                  <v-text-field
                    v-model="newLine.end"
                    v-maska="'##:##'"
                    placeholder="17:00"
                    label="Tot"
                    prepend-icon="mdi-clock-time-four-outline"
                    :error-messages="translateErrors(errors.end, 'Tot')"
                  />
                </v-card-text>
                <v-card-actions>
                  <v-btn
                    text="Annuleren"
                    @click="isActive.value = false"
                  />
                  <v-btn
                    text="Opslaan"
                    color="primary"
                    variant="elevated"
                    @click="saveWorkdayLine(day, isActive);"
                  />
                </v-card-actions>
              </v-card>
            </template>
          </v-dialog>
          <v-btn
            prepend-icon="mdi-clock-plus-outline"
            :disabled="day.status !== 'new' || !day.id"
            class="ml-2"
            @click="openSpecialHoursDialog(day)"
          >
            Bijzondere uren
          </v-btn>
          <v-checkbox
            v-model="day.status"
            label="Afgerond"
            density="compact"
            false-value="new"
            true-value="processed"
            class="mr-2"
            color="primary"
            hide-details
            :disabled="!day.id"
            @change="changedWorkdays[day.date] = true"
          />
          <v-btn
            color="primary"
            variant="elevated"
            :prepend-icon="day.id ? 'mdi-content-save' : 'mdi-plus'"
            :disabled="day.id && !changedWorkdays[day.date]"
            :loading="loading[day.date]"
            @click="saveWorkday(day)"
          >
            {{ day.id ? 'Opslaan' : 'Werkdag aanmaken' }}
          </v-btn>
        </v-card-actions>
      </v-expansion-panel-text>
    </v-expansion-panel>
  </v-expansion-panels>
  <v-fab
    v-if="Object.keys(changedWorkdays).length"
    size="large"
    color="primary"
    location="top right"
    prepend-icon="mdi-content-save"
    app
    @click="saveAllWorkdays"
  >
    Sla alles op ({{ Object.keys(changedWorkdays).length }})
  </v-fab>

  <workday-edit-dialog
    v-model="editDialog"
    :workday="editingWorkday"
    @saved="refreshWorkdays"
  />

  <special-hours-edit-dialog
    v-model="specialHoursDialog"
    :workday="editingSpecialHoursWorkday"
    :special-hours="editingSpecialHours"
    @saved="refreshSpecialHours"
  />

  <confirm-dialog
    v-model="showLockConfirm"
    title="Maand afsluiten"
    message="Weet je zeker dat je deze maand wilt afsluiten? Dit kan niet ongedaan worden gemaakt."
    :loading="lockingMonth"
    @confirm="confirmLockMonth"
    @cancel="cancelLockMonth"
  />
</template>

<style lang="scss">
tr.workday-line td:first-child {
  border-left: 10px solid lightgray;
}

tr.void td:first-child {
  border-left: 10px solid rgba(var(--v-theme-indufastRed), .75);
}

tr.void {
  background-color: rgba(var(--v-theme-indufastRed), .1);
}

tr.travel-to td:first-child {
  border-left: 10px solid rgba(var(--v-theme-indufastGreen), .75);
}

tr.travel-to {
  background-color: rgba(var(--v-theme-indufastGreen), .1);
}

tr.travel-from td:first-child {
  border-left: 10px solid rgba(var(--v-theme-indufastCyan), .75);
}

tr.travel-from {
  background-color: rgba(var(--v-theme-indufastCyan), .1);
}

tr.special-hours-line td:first-child {
  border-left: 10px solid lightgray;
}

tr.marked-for-deletion td:first-child {
  border-left: 10px solid rgba(var(--v-theme-indufastRed), .75);
}

tr.marked-for-deletion {
  background-color: rgba(var(--v-theme-indufastRed), .1);
}

.v-icon--disabled {
  color: grey !important;
}

.workday-title {
  background-color: rgba(var(--v-theme-indufastCyan), .1);
}

td.no-wrap {
  white-space: nowrap;
  width: 0;
}
tr.netDuration,
tr.totalDuration {
  font-weight: bold;
}

div.summary {
  width: 300px;
  display: inline-block;
  vertical-align: top;
}

div.lines {
  width: calc(100% - 300px);
  display: inline-block;
  vertical-align: top;
}

.summary {
  min-width: 280px;
}

.summary td :not(.wrap) {
  white-space: nowrap;
}

.summary table {
  margin-bottom: 10px;
}

.summary th {
  font-weight: normal;
  text-align: left;
  width: 120px;
}
.summary .totalDuration th {
  font-weight: bold;
}
.summary tr.totalDuration th,
.summary tr.totalDuration td,
.summary tr.netDuration th,
.summary tr.netDuration td {
  padding-bottom: 10px !important;
}

.totalSummary th {
  padding-right: 15px;
}
.totalSummary td {
  text-align: right;
}

.weekend {
  background-color: rgba(var(--v-theme-indufastYellow), var(--v-extra-light-opacity));
}

.holiday-workday {
  background-color: rgba(var(--v-theme-indufastGreen), var(--v-extra-light-opacity));
}

.new .v-expansion-panel-title:not(.v-expansion-panel-title--active) {
  color: grey;
}
.overriden {
  background-color: rgba(var(--v-theme-indufastRed), var(--v-extra-light-opacity));
  border-radius: 5px;
  padding-left: 5px;
  padding-right: 5px;
}
</style>
