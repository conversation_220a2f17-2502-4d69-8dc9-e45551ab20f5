<?php

  use classes\HydrateTrait;
  use classes\LogTrait;

  AppModel::loadModelClass('IndufastProjectModel');

  class IndufastProject extends IndufastProjectModel {

    use PropertyCastTrait {
      castProperties as castPropertiesTrait;
    }
    use ModelFillTrait;
    use ValidationTrait;
    use LogTrait;
    use HydrateTrait;

    /** @var IndufastCalendarEvent[] */
    public ?array $events = null;
    public ?string $start = null;
    public ?string $end = null;
    public ?string $plannable = null;
    public array $default_relations = ['events', 'plannable'];

    /**
     * @deprecated Use event type instead.
     */
    const STATUS_BLAST_SAND = 'blast_sand';
    const STATUS_PLANNING = 'planning';
    const STATUS_INCOMPLETE = 'incomplete';
    const STATUS_WEATHER_DEPENDENT = 'weather_dependent';
    const STATUS_COMPLETE = 'complete';
    const STATUS_UNPLANNED = 'unplanned';

    const MATERIAL_LOAD_BLADEL = 'bladel';
    const MATERIAL_LOAD_ALBLASSERDAM = 'alblasserdam';
    const PLANNABLE = 'plannable';
    const NOT_PLANNABLE_INTERNAL = 'not_plannable_internal';
    const NOT_PLANNABLE_EXTERNAL = 'not_plannable_external';

    const int COLOR_ID_SAGE = 2;
    const int COLOR_ID_BANANA = 5;
    const int COLOR_ID_TANGERINE = 6;
    const int COLOR_ID_PEACOCK = 7;
    const int COLOR_ID_TOMATO = 11;

    const CAST_PROPERTIES = [
      'id'                        => 'int',
      'fte'                       => 'int',
      'void'                      => 'boolean',
      'from_db'                   => 'hidden',
      'insertTS'                  => 'hidden',
      'updateTS'                  => 'hidden',
    ];

    protected array $fillable = [
      'name'                      => 'required|string|max:255',
      'remark'                    => 'nullable|string',
      'fte'                       => 'nullable|integer|min:1|max:100',
      'project_number'            => 'required|string|max:32|unique:indufast_project,project_number,id,{id}',
      'project_number_exact'      => 'required|string|max:32|unique:indufast_project,project_number_exact,id,{id}',
      'customer_name'             => 'required|string|max:255',
      'contact_name'              => 'nullable|string|max:255',
      'contact_email'             => 'nullable|email|max:255',
      'contact_number'            => 'nullable|string|max:32',
      'address'                   => 'required|string|max:255',
      'void'                      => 'boolean',
    ];

    public function getFillable(): array {
      $this->fillable['status'] = 'string|required|in:' . implode(',', [
        self::STATUS_BLAST_SAND,
        self::STATUS_PLANNING,
        self::STATUS_INCOMPLETE,
        self::STATUS_WEATHER_DEPENDENT,
        self::STATUS_COMPLETE,
        self::STATUS_UNPLANNED,
      ]);

      $this->fillable['material_load'] = 'string|nullable|in:' . implode(',', [
        self::MATERIAL_LOAD_BLADEL,
        self::MATERIAL_LOAD_ALBLASSERDAM,
      ]);

      return $this->fillable;
    }

    public function parent_name() {
      return self::class;
    }

    public function parent_id() {
      return $this->id;
    }

    /**
     * Find projects by various data filters.
     *
     * @return IndufastProject[]
     * @throws Exception
     */
    public static function find_by_data(array $data): array {
      $where = ['1'];
      $eventWhere = [];
      $eventJoins = [];

      // @TODO: does not work with multiday events.
      if (isset($data['date'])) {
        $eventWhere[] = sprintf("start >= '%s' AND end <= '%s'",
          $data['date'] . ' 00:00:00',
          $data['date'] . ' 23:59:59',
        );
      }

      if (isset($data['archive'])) {
        $where[] = 'ip.void = ' . (($data['archive'] == 'true') ? '1' : '0');
      }

      if (isset($data['year'])) {
        $eventWhere[] = sprintf("YEAR(start) = %d", $data['year']);

        if (isset($data['month'])) {
          $eventWhere[] = sprintf("MONTH(start) = %d", $data['month']);
        }
      }

      if (isset($data['employee_id'])) {
        $eventJoins[] = sprintf(
          'JOIN indufast_calendar_event_employee AS ice ON ice.calendar_event_id = indufast_calendar_event.id AND ice.employee_id = %d',
          (int)$data['employee_id'],
        );
      }

      if (isset($data['google_calendar_event_ids'])) {
        $eventWhere[] = DbHelper::getSqlIn('google_calendar_event_id', $data['google_calendar_event_ids']);
      }

      if ($eventWhere || $eventJoins) {
        if ($events = IndufastCalendarEvent::find_all(implode(' ', [
          implode(' ', $eventJoins),
          ($eventWhere) ? ' WHERE ' . implode(' AND ', $eventWhere) : ''
        ]))) {
          $where[] = DbHelper::getSqlIn('ip.id', array_unique(array_column($events, 'project_id')));
        }
        else {
          return [];
        }
      }

      $query = 'SELECT employee_id, COUNT(*) AS count FROM indufast_workday WHERE status = "new" GROUP BY employee_id';
      $result = DBConn::db_link()->query($query);
      $employeeNewWorkdayCounts = [];
      while ($row = $result->fetch_assoc()) {
        $employeeNewWorkdayCounts[$row['employee_id']] = (int)$row['count'];
      }

      $query = 'SELECT employee_id, COUNT(*) AS count FROM indufast_workday_summary WHERE locked = 0 GROUP BY employee_id';
      $result = DBConn::db_link()->query($query);
      $employeeOpenMonthCounts = [];
      while ($row = $result->fetch_assoc()) {
        $employeeOpenMonthCounts[$row['employee_id']] = (int)$row['count'];
      }

      $query = sprintf(
        "SELECT %s FROM indufast_project AS ip
        LEFT JOIN indufast_calendar_event AS ice ON ice.project_id = ip.id
        LEFT JOIN indufast_calendar_event_employee AS icee ON icee.calendar_event_id = ice.id
        LEFT JOIN indufast_employee AS ie ON icee.employee_id = ie.id
        WHERE %s
        ORDER BY ip.id DESC",
        implode(', ', array_merge(
          self::getModelColumnSelect(IndufastProject::class, 'ip'),
          self::getModelColumnSelect(IndufastCalendarEvent::class, 'ice'),
          self::getModelColumnSelect(IndufastCalendarEventEmployee::class, 'icee'),
          self::getModelColumnSelect(IndufastEmployee::class, 'ie'),
        )),
        implode(' AND ', $where),
      );

      $result = DBConn::db_link()->query($query);
      $projects = [];
      $events = [];
      while ($row = $result->fetch_assoc()) {
        $project = new IndufastProject()->hydrateRow($row, 'ip');
        $project->events = [];
        $projects[$project->id] = $projects[$project->id] ?? $project;

        $event = new IndufastCalendarEvent()->hydrateRow($row, 'ice');
        if ($event->id) {
          $event->employees = [];
          $event->employee_ids = [];
          $events[$event->id] = $events[$event->id] ?? $event;

          $eventEmployee = new IndufastCalendarEventEmployee()->hydrateRow($row, 'icee');
          if ($eventEmployee->id) {
            $eventEmployee->employee = new IndufastEmployee()->hydrateRow($row, 'ie');
            $eventEmployee->employee->newWorkdayCount = $employeeNewWorkdayCounts[$eventEmployee->employee_id] ?? 0;
            $eventEmployee->employee->openMonthCount = $employeeOpenMonthCounts[$eventEmployee->employee_id] ?? 0;
            $events[$eventEmployee->calendar_event_id]->employees[] = $eventEmployee;
            $events[$eventEmployee->calendar_event_id]->employee_ids[] = (int) $eventEmployee->employee->id;
          }
        }
      }

      /** @var IndufastCalendarEvent $event */
      foreach ($events as $event) {
        $material_load_employee_ids = json_decode($event->material_load_employee_ids ?? '[]', true);

        foreach ($event->employees as $eventEmployee) {
          if ($event->team_lead_employee_id && !$event->team_lead && $eventEmployee->employee_id == $event->team_lead_employee_id) {
            $event->team_lead = $eventEmployee->employee;
          }

          if ($event->material_load_employee_ids && !in_array($eventEmployee->employee_id, $material_load_employee_ids)) {
            $event->material_loaders[$eventEmployee->employee_id] = $eventEmployee->employee;
          }
        }

        $event->material_loaders = array_values($event->material_loaders ?? []);
        $projects[$event->project_id]->events[] = $event;
      }

      return array_values($projects);
    }

    public function getColorId(): int {
      return match ($this->status) {
        self::STATUS_BLAST_SAND => self::COLOR_ID_BANANA,
        self::STATUS_PLANNING => self::COLOR_ID_PEACOCK,
        self::STATUS_INCOMPLETE => self::COLOR_ID_TOMATO,
        self::STATUS_WEATHER_DEPENDENT => self::COLOR_ID_TANGERINE,
        self::STATUS_COMPLETE => self::COLOR_ID_SAGE,
        default => 0,
      };
    }

    public function castProperties(): void {
      $this->castPropertiesTrait();
      $this->plannable();

      $first = null;
      $last = null;
      foreach ($this->events() as $event) {
        $first = min($first ?? $event->start, $event->start);
        $last = max($last, $event->end);

        $event->castProperties();
      }

      $this->start = $first;
      $this->end = $last;
      $this->event_count = $this->events ? count($this->events) : null;
    }

    public function save(array &$errors = []): mysqli_result|bool {
      $action = $this->id ? 'update_entity' : 'create_entity';
      $this->castPropertiesForSave();
      $result = parent::save($errors);
      $this->logOperation($action);
      return $result;
    }

    /**
     * Format the project data for the Google Calendar event description.
     * @throws DateMalformedStringException
     */
    public function getCalenderDescription(IndufastCalendarEvent $event): string {
      $remark = str_replace(["\n", "\r"], '', $this->remark ?? '');
      $remark = preg_replace('/(<p>(?:&nbsp;|\s)*<\/p>)*$/', '', $remark);

      $load = '';
      if ($this->material_load) {
        $load = ($event->materialLoaders()) ? implode(', ', array_column($event->material_loaders, 'name')) . ' (' . ucfirst($this->material_load) . ')': ucfirst($this->material_load);
      }

      $input = [
        'Extra opmerking'           => escapeSafe($event->remark),
        'Klantnaam'                 => escapeSafe($this->customer_name),
        'Contactpersoon'            => escapeSafe(implode(", ", array_filter([
          $this->contact_name,
          $this->contact_number,
          $this->contact_email,
        ]))),
        'Projectnummer'             => escapeSafe($this->project_number),
        'Aanvangstijd'              => new DateTime($event->start)->format('H:i') . ' - Minimaal 15 minuten voor aanvang werkzaamheden aanwezig zijn.',
        'Materiaal laden'           => $load,
        'Contactpersoon bouwplaats' => ($event->teamLead()) ? $event->team_lead->name : '',
        'Omschrijving'              => $remark,
        'Let op'                    => '<ul><li>Afval altijd netjes gestapeld achterlaten als er geen container is.</li><li>Vloer nameten en dit doorgeven per <NAME_EMAIL>.</li><li>Verbruik van mortel, thix, gel altijd noteren en doorgeven.</li></ul>',
      ];

      $output = [];
      foreach ($input as $title => $content) {
        if ($content) {
          $output[] = '<p><b><u>' . escapeSafe($title) . '</u>:</b> ' . $content . '</p>';
        }
      }

      return strip_tags(implode("", $output), ['p', 'b', 'br', 'ul', 'ol', 'li', 'i', 'em', 'u', 'strong']);
    }

    public function plannable(): string {
      foreach ($this->events() as $event) {
        foreach ($event->employees() as $employee) {
          if ($employee->conflict) {
            $conflict = is_string($employee->conflict) ? json_decode($employee->conflict) : $employee->conflict;
            if ($conflict && isset($conflict->internal)) {
              if ($conflict->internal) {
                return $this->plannable = self::NOT_PLANNABLE_INTERNAL;
              }
              return $this->plannable = self::NOT_PLANNABLE_EXTERNAL;
            }
          }
        }
      }
      return $this->plannable = self::PLANNABLE;
    }

    /**
     * @return IndufastCalendarEvent[]
     */
    public function events(): array {
      return (is_array($this->events)) ? $this->events : $this->events = IndufastCalendarEvent::find_all_by(['project_id' => $this->id], 'ORDER BY start');
    }

  }
