<?php

  use classes\HydrateTrait;
  use classes\LogTrait;
  use classes\TimesTrait;

  AppModel::loadModelClass('IndufastWorkdayModel');

  class IndufastWorkday extends IndufastWorkdayModel {

    use PropertyCastTrait;
    use ModelFillTrait;
    use ValidationTrait;
    use TimesTrait;
    use LogTrait;
    use HydrateTrait;

    /** @var IndufastWorkdayLine[] */
    public ?array $lines = null;
    /** @var IndufastWorkdaySpecialHours[] */
    public ?array $specialHours = null;
    public IndufastEmployee $employee;
    public string $travelToStart = '';
    public string $travelToEnd = '';
    public float $travelToDistance = 0;
    public string $travelToDuration = '';
    public string $travelToDurationNet = '';
    public string $workdayStart = '';
    public string $workdayEnd = '';
    public string $workdayDuration = '';
    public string $workdayPause = '';
    public string $workdayDurationNet = '';
    public string $workdayDurationNetClipped = '';
    public string $workdayDurationSaturdayNet = '';
    public string $workdayDurationSundayOrHolidayNet = '';
    public string $overtimeBelow = '';
    public string $overtimeAbove = '';
    public string $workdayOutsideWorkingHours = '';
    public string $travelFromStart = '';
    public string $travelFromEnd = '';
    public float $travelFromDistance = 0;
    public string $travelFromDuration = '';
    public string $travelFromDurationNet = '';
    public string $travelDurationNet = '';
    public string $specialHoursDuration = '';
    public string $specialHoursLeave = '';
    public string $specialHoursSpecialLeave = '';
    public string $specialHoursSick = '';

    const string STATUS_NEW = 'new';
    const string STATUS_PROCESSED = 'processed';
    const string STATUS_SYNCED = 'synced';
    const string OVERTIME_LIMIT = '03:00:00';
    const string OUTSIDE_HOURS_START = '06:00:00';
    const string OUTSIDE_HOURS_END = '18:00:00';
    const string WORKDAY_LENGTH = '07:30:00';
    const string BREAK = '00:45:00';
    const string LONG_BREAK = '01:00:00';
    const string LONG_BREAK_THRESHOLD = '10:00:00';
    const int TRAVEL_TIME_THRESHOLD_MINUTES = 45;
    const float HOURS_PER_MONTH = 162.5;

    const array CAST_PROPERTIES = [
      'id'                        => 'int',
      'travel_to_end_line_id'     => 'int',
      'travel_from_start_line_id' => 'int',
      'employee'                  => 'hidden',
      'employee_id'               => 'int',
      'from_db'                   => 'hidden',
      'insertTS'                  => 'hidden',
      'updateTS'                  => 'hidden',
    ];

    protected array $fillable = [
      'remark'                    => 'nullable|string',
      'employee_id'               => 'integer|required|exists:indufast_employee,id',
      'date'                      => 'required|date:Y-m-d|unique:indufast_workday,date,id,{id},employee_id,{employee_id}|locked_month',
      'travel_to_end_line_id'     => 'integer|exists:indufast_workday_line,id',
      'travel_from_start_line_id' => 'integer|exists:indufast_workday_line,id',
      'break_time'                => 'nullable|date:H:i:s',
      'work_time'                 => 'nullable|date:H:i:s',
    ];

    public array $default_relations = ['lines', 'specialHours', 'calculate'];

    public function getFillable(): array {
      $this->fillable['status'] = 'nullable|string|in:' . implode(',', [self::STATUS_NEW, self::STATUS_PROCESSED, self::STATUS_SYNCED]);
      return $this->fillable;
    }

    public function parent_name(): string {
      return self::class;
    }

    public function parent_id(): int {
      return $this->id;
    }

    /**
     * @return IndufastWorkdayLine[]
     */
    public function lines(): array {
      return (is_array($this->lines)) ? $this->lines : $this->lines = IndufastWorkdayLine::find_all_by(['workday_id' => $this->id], 'ORDER BY start');
    }

    /**
     * @return IndufastWorkdaySpecialHours[]
     */
    public function specialHours(): array {
      return (is_array($this->specialHours)) ? $this->specialHours : $this->specialHours = IndufastWorkdaySpecialHours::find_all_by(['workday_id' => $this->id], 'ORDER BY type, duration');
    }

    public function employee(): IndufastEmployee {
      return $this->employee ?? $this->employee = IndufastEmployee::find_by_id($this->employee_id);
    }

    public function setDefaults(): void {
      parent::setDefaults();
      $this->insertTS = date("Y-m-d H:i:s");
    }

    public function calculate(): void {
      $this->calculateSpecialHours();

      if (!$this->travel_to_end_line_id && !$this->travel_from_start_line_id && !$this->work_time && !$this->specialHoursDuration) {
        return;
      }

      $holidays = IndufastCalendarEvent::holidays();
      $max = '00:00:00';
      foreach ($this->lines() as $line) {
        if ($line->void) {
          continue;
        }

        if ($line->id == $this->travel_from_start_line_id) {
          $this->travelFromStart = $line->start;
          $this->workdayEnd = $line->start;
        }

        if (!$this->travelToEnd && !$this->travelFromStart && $this->travel_to_end_line_id) {
          $line->type = 'travel-to';
          $max = max($line->end, $max);
          if (!$this->travelToStart) {
            $this->travelToStart = $line->start;
          }
        }
        elseif ($this->travelFromStart) {
          $line->type = 'travel-from';
          $this->travelFromEnd = max($line->end, $this->travelFromEnd);
          $this->travelFromDuration = $this->duration($this->travelFromStart, $this->travelFromEnd);
          $this->travelFromDurationNet = $this->duration($this->travelFromStart, $this->travelFromEnd, self::TRAVEL_TIME_THRESHOLD_MINUTES);
        }
        else {
          $line->type = null;
        }

        if ($line->id == $this->travel_to_end_line_id) {
          $this->travelToEnd = max($line->end, $max);
          $this->travelToDuration = $this->duration($this->travelToStart, $this->travelToEnd);
          $this->travelToDurationNet = $this->duration($this->travelToStart, $this->travelToEnd, self::TRAVEL_TIME_THRESHOLD_MINUTES);
          $this->workdayStart = $this->travelToEnd;
        }
      }

      $this->calculateTravelDistance();

      // Calculate total travel time.
      $this->travelDurationNet = $this->addTimes(array_filter([$this->travelToDurationNet, $this->travelFromDurationNet]));

      // Calculate workday and overtime.
      if (($this->workdayStart && $this->workdayEnd) || $this->work_time) {
        $this->workdayDuration = $this->work_time ?? $this->duration($this->workdayStart, $this->workdayEnd);

        // Use manual break_time if set, otherwise calculate.
        if ($this->break_time) {
          $this->workdayPause = $this->break_time;
        }
        else {
          $this->workdayPause = ($this->workdayDuration >= self::LONG_BREAK_THRESHOLD) ? self::LONG_BREAK : self::BREAK;
        }

        // Use manual work_time if set, otherwise calculate.
        $workdayNet = $this->subtractTimes($this->workdayDuration, $this->workdayPause, false);
        $this->workdayDurationNet = $workdayNet;
        $this->workdayDurationNetClipped = min($workdayNet, self::WORKDAY_LENGTH);

        $dayOfWeek = date('N', strtotime($this->date));
        $isHoliday = in_array($this->date, array_values($holidays));
        
        if ($dayOfWeek == 6) {
          $this->workdayDurationSaturdayNet = $workdayNet;
        }
        elseif ($dayOfWeek == 7 || $isHoliday) {
          $this->workdayDurationSundayOrHolidayNet = $workdayNet;
        }

        $overtime = $this->subtractTimes($this->workdayDurationNet, self::WORKDAY_LENGTH, false);

        // If work_time is set, we don't calculate outside working hours because we don't knwo the start of the workday.
        if (!$this->work_time) {
          $this->calculateOutsideWorkingHours($overtime);
        }

        // Split the overtime into below and above 3 hours.
        if ($overtime <= self::OVERTIME_LIMIT) {
          $this->overtimeBelow = ($overtime != '00:00:00') ? $overtime : '';
        }
        else {
          $this->overtimeBelow = self::OVERTIME_LIMIT;
          $this->overtimeAbove = $this->subtractTimes($overtime, self::OVERTIME_LIMIT);
        }
      }
      else {
        $this->workdayStart = '';
      }
    }

    protected function calculateSpecialHours(): void {
      $leaveHours = [];
      $specialLeaveHours = [];
      $sickHours = [];
      $unexcusedLeaveHours = [];
      $allSpecialHours = [];

      foreach ($this->specialHours() as $specialHours) {
        $allSpecialHours[] = $specialHours->duration;

        switch ($specialHours->type) {
          case IndufastWorkdaySpecialHours::TYPE_LEAVE:
            $leaveHours[] = $specialHours->duration;
            break;
          case IndufastWorkdaySpecialHours::TYPE_SPECIAL_LEAVE:
            $specialLeaveHours[] = $specialHours->duration;
            break;
          case IndufastWorkdaySpecialHours::TYPE_SICK:
            $sickHours[] = $specialHours->duration;
            break;
          case IndufastWorkdaySpecialHours::TYPE_UNEXCUSED_LEAVE:
            $unexcusedLeaveHours[] = $specialHours->duration;
            break;
        }
      }

      $this->specialHoursLeave = !$leaveHours ? '' : $this->addTimes($leaveHours);
      $this->specialHoursSpecialLeave = !$specialLeaveHours ? '' : $this->addTimes($specialLeaveHours);
      $this->specialHoursSick = !$sickHours ? '' : $this->addTimes($sickHours);
      $this->specialHoursUnexcusedLeave = !$unexcusedLeaveHours ? '' : $this->addTimes($unexcusedLeaveHours);
      $this->specialHoursDuration = !$allSpecialHours ? '' : $this->addTimes($allSpecialHours);
    }

    /**
     * Calculate the workday duration outside regular working hours.
     * Hours booked on a non-working day are considered outside working hours.
     */
    protected function calculateOutsideWorkingHours(&$overtime): void {
      if ($this->employee()->isNonWorkingDay($this->date)) {
        $this->workdayOutsideWorkingHours = $this->workdayDurationNet;
      }
      else {

        // Subtract overtime from the workday duration outside regular hours if the day started outside the regular hours.
        if ($this->workdayStart < self::OUTSIDE_HOURS_START) {
          $this->workdayOutsideWorkingHours = $this->duration($this->workdayStart, self::OUTSIDE_HOURS_START);
          $overtime = $this->subtractTimes($overtime, $this->workdayOutsideWorkingHours, false);
        }

        if ($this->workdayStart >= self::OUTSIDE_HOURS_END) {
          $this->workdayOutsideWorkingHours = $this->workdayDurationNet;
        }
      }
    }

    private function timeToMinutes(string $time): int {
      [$hours, $minutes] = explode(':', $time);
      return (int)$hours * 60 + (int)$minutes;
    }

    /**
     * @return IndufastWorkday[]
     */
    public static function find_all_by_data(array $data): array {
      $where = [1];

      if (isset($data['year'])) {
        $where[] = sprintf("YEAR(iw.date) = %d", $data['year']);

        if (isset($data['month'])) {
          $where[] = sprintf("MONTH(iw.date) = %d", $data['month']);
        }
      }

      if (isset($data['status'])) {
        $where[] = DbHelper::getSqlIn('iw.status', (array)$data['status']);
      }

      $query = sprintf(
        "SELECT %s FROM indufast_workday AS iw
        LEFT JOIN indufast_workday_line AS iwl ON iwl.workday_id = iw.id
        LEFT JOIN indufast_workday_special_hours AS iwsh ON iwsh.workday_id = iw.id
        JOIN indufast_employee AS ie ON iw.employee_id = ie.id
        WHERE %s
        ORDER BY iw.date",
        implode(', ', array_merge(
          self::getModelColumnSelect(IndufastWorkday::class, 'iw'),
          self::getModelColumnSelect(IndufastWorkdayLine::class, 'iwl'),
          self::getModelColumnSelect(IndufastWorkdaySpecialHours::class, 'iwsh'),
          self::getModelColumnSelect(IndufastEmployee::class, 'ie'),
        )),
        implode(' AND ', $where),
      );

      $result = DBConn::db_link()->query($query);
      $workdays = [];
      while ($row = $result->fetch_assoc()) {
        $workday = new IndufastWorkday()->hydrateRow($row, 'iw');
        $workday->lines = [];
        $workday->specialHours = [];

        $employee = new IndufastEmployee()->hydrateRow($row, 'ie');
        if ($employee->id) {
          $workday->employee = $employee;
        }

        $workdays[$workday->id] = $workdays[$workday->id] ?? $workday;

        $workdayLine = new IndufastWorkdayLine()->hydrateRow($row, 'iwl');
        if ($workdayLine->id) {
          $workdayLine->workday = $workdays[$workday->id];
          $workdays[$workday->id]->lines[$workdayLine->id] = $workdayLine;
        }

        $specialHours = new IndufastWorkdaySpecialHours()->hydrateRow($row, 'iwsh');
        if ($specialHours->id) {
          $workdays[$workday->id]->specialHours[$specialHours->id] = $specialHours;
        }
      }

      foreach ($workdays as $workday) {
        $workday->lines = array_values($workday->lines);
        $workday->specialHours = array_values($workday->specialHours);
      }

      return array_values($workdays);
    }

    public static function getNewCountByUserId(int $userId): int {
      $query = sprintf(
        'SELECT COUNT(*) AS count FROM indufast_workday WHERE employee_id = %d AND status = "%s"',
        $userId,
        self::STATUS_NEW
      );
      return (int)(DBConn::db_link()->query($query))->fetch_assoc()['count'] ?? 0;
    }

    public static function getOpenMonthsByUserId(int $userId): int {
      $query = sprintf(
        'SELECT COUNT(*) AS count FROM indufast_workday_summary WHERE employee_id = %d AND locked = 0',
        $userId,
      );
      return (int)(DBConn::db_link()->query($query))->fetch_assoc()['count'] ?? 0;
    }

    public function calculateTravelDistance() {
      $toFound = false;
      $fromFound = false;

      foreach ($this->lines() as $line) {
        if ($line->void || !$line->distance) {
          continue;
        }

        if ($this->travel_to_end_line_id && !$toFound) {
          $this->travelToDistance += $line->distance;
          $toFound = ($line->id == $this->travel_to_end_line_id);
        }

        $fromFound = $fromFound || ($line->id == $this->travel_from_start_line_id);
        if ($this->travel_from_start_line_id && $fromFound) {
          $this->travelFromDistance += $line->distance;
        }
      }

      $this->travelToDistance = round($this->travelToDistance, 1);
      $this->travelFromDistance = round($this->travelFromDistance, 1);
    }
  }