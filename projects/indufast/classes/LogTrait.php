<?php

  namespace classes;

  use IndufastLog;
  use mysqli_result;
  use stdClass;

  trait LogTrait {
    public function save(array &$errors = []): mysqli_result|bool {
      $action = $this->id ? 'update_entity' : 'create_entity';
      $result = parent::save($errors);
      $this->logOperation($action);
      return $result;
    }

    public function destroy(): mysqli_result|bool {
      $result = parent::destroy();
      $this->logOperation('destroy_entity');
      return $result;
    }

    public static function delete_by(?array $conditions, string $raw_sql = ''): bool {
      $entities = self::find_all_by($conditions, $raw_sql);
      foreach ($entities as $entity) {
        $entity->destroy();
      }
      return true;
    }

    public function logOperation(string $action): void {
      $log = new IndufastLog();
      $log->action = $action;
      $log->user_id = $_SESSION['userObject']?->id ?? 0;
      $log->entity_name = get_class($this);
      $log->entity_id = $this->id;

      $log->parent_name = $this->parent_name();
      $log->parent_id = $this->parent_id();

      $this->castPropertiesToTypes();
      $object = $this->getWithDatabaseProperties();

      $log->change_data = json_encode($object);
      $log->changed_at = date('Y-m-d H:i:s');
      $log->save();
    }

    public function getWithDatabaseProperties(): object {
      $database_properties = new stdClass();
      foreach (self::columns as $column) {
        $database_properties->{$column} = $this->{$column} ?? null;
      }
      return $database_properties;
    }
  }
