<?php

  use <PERSON>\Debugger;

  ini_set('display_errors', true);
  error_reporting(E_ALL);


  if (!defined('PHP_VERSION_ID') || PHP_VERSION_ID < 80100) {
    // PHP version should be ^8.1
    echo "PHP version is lower than 8.1. Current version: " . phpversion();
    mail("<EMAIL>", "PHP version too low: " . phpversion(), "PHP version is lower than 8.1. Current version: " . phpversion());
    exit;
  }

//normaliter staat dit goed in de php.ini. Maar bij sommige hostingproviders niet. Gezet voor de zekerheid.
  date_default_timezone_set('Europe/Amsterdam');

  require_once("gsdfw/includes/classes/Config.php");
  require_once("gsdfw/includes/classes/Context.php");
  require_once("config/configure.inc.php");
  require_once(DIR_CLASSES . "GsdAutoloader.php");
  require_once(DIR_INCLUDES . 'vendor/autoload.php');
  require_once(DIR_INCLUDES . "regex.inc.php");
  __autoloader('Trans');
  require_once(DIR_INCLUDES . "functions.inc.php");
  require_once(DIR_INCLUDES . "logging.inc.php");

  if (MAIL_ALL_ERRORS) {
    ini_set('display_errors', false);
    set_error_handler("errorHandler");
    register_shutdown_function("shutdownHandler");
  }
  elseif (DEVELOPMENT) {
    error_reporting(E_ALL);

    // let op: als je een witte pagina krijgt met een 500 internal server error, wil dit zeggen dat er output plaats vindt voordat Tracy draait
    // controleer de ingeladen bestanden hierboven of er geen enter/spatie staat voor de <?php tag
    if (!Config::isdefined("TRACY_ENABLED") || Config::get("TRACY_ENABLED")) {
      // Tracy debugger, see: https://tracy.nette.org/
      // disable on ajax calls, because it will not show PHP exceptions
      $isAjaxCall = (isset($_SERVER["HTTP_ACCEPT"]) && $_SERVER["HTTP_ACCEPT"] == "application/json") || !empty($_SERVER['HTTP_X_TRACY_AJAX']);
      Debugger::enable($isAjaxCall, LOG_FOLDER); //ajax call, then production mode = errors to logs folder
      Debugger::$editorMapping = [
        DIR_ROOT => DOCKER_DIR_ROOT, // Map server paths to local paths
      ];
//      Debugger::$editor = 'phpstorm://vscode-remote/wsl+Ubuntu22.04/home/<USER>/htdocs/fugamo/fugamo-shop/%file:%line';
      Debugger::$maxLength = 0; // unlimited chars when dumping strings
      Debugger::$maxDepth = 8; // depth of arrays/objects shown when dumping
      Debugger::$strictMode = true; //standard strictMode true, so we are forced to write valid code
      Debugger::getBar()->addPanel(new TracyDebugPanelFiles()); // add a panel with template and action files of the current page
      Debugger::getBar()->addPanel(new TracyDebugPanelDatabase()); // add a panel with all run sql queries on the current page
      Debugger::getBar()->addPanel(new TracyDebugPanelPost()); // add a panel with all $_POST values, if they are set
    }

  }

  if (!DEVELOPMENT) {
    //op sommige servers is het niet mogelijk om de session_save_path te zetten vanuit .htaccess. (NGINX)
    if (Config::isdefined("SESSION_SAVE_PATH")) session_save_path(Config::get("SESSION_SAVE_PATH"));
    if (Config::isdefined("SESSION_GC_MAXLIFETIME")) {
      //LET OP: de session.gc_maxlifetime word overschreven op de server. Deze moet je expliciet zetten in direct admin, op de PHP options pagina.
      ini_set('session.gc_maxlifetime', Config::get("SESSION_GC_MAXLIFETIME"));
      session_set_cookie_params(Config::get("SESSION_GC_MAXLIFETIME"));
    }
  }

  $session_started = session_start();

  GsdApplication::init();

  header('Content-Type: text/html; charset=utf-8');

//-------------bepaal site en dus ook template -------------

// if(DEVELOPMENT) {
  if (isset($_SESSION['site'])) {
    unset($_SESSION['site']);
  }
// }

  $site = null;

  if (!isset($_SESSION['site'])) {

    if (ENVIRONMENT == 'LOCAL') {

      $siteid = substr($_SERVER['HTTP_HOST'], 0, strpos($_SERVER['HTTP_HOST'], '.'));
      if (preg_match('/(?P<site_host>.*)\.(?P<project>.*)\.localhost$/', $_SERVER['HTTP_HOST'], $matches)) {
        // New localhost structure.
        $site = Site::find_by_host($matches['site_host']);
        if (!$site) {
          //ook even zoeken op met www.
          $site = Site::find_by_host($matches['site_host'], true);
        }
        if (!$site) {
          throw new GsdException('DEVELOPER MESSAGE: site not found??');
        }
        $site->site_host->host = $_SERVER['HTTP_HOST'];
        $site->site_host->type = SiteHost::TYPE_SUB;
      }
      elseif (preg_match('/^la[0-9]{4}$/', $siteid)) {
        // @deprecated: vanuit klapjap.nl
        $siteid = substr($siteid, 2); //la verwijderen
        $siteid = intval($siteid); //site id
        $site = Site::find_by_id($siteid);
        if ($site) {
          $site->site_host = SiteHost::getPrimary($siteid);
          if (!$site->site_host) {
            $site = false;
          }
          else {
            $site->site_host->host = $_SERVER['HTTP_HOST'];
            $site->site_host->type = SiteHost::TYPE_SUB;
          }
        }
      }
      elseif ($_SERVER['SERVER_PORT'] > 8000 && $_SERVER['SERVER_PORT'] <= 8020) {

        // Use port 8001-8020 to connect to localhost website on the same network.
        // 192.168.0.XX::8001 goes to site id 1 (la0001.klapjap.nl), 192.168.0.XX::8002 goes to site id 2 (la0002.klapjap.nl), etc.
        $site = Site::find_by_id($_SERVER['SERVER_PORT'] - 8000);

        if ($site) {
          $site->site_host = SiteHost::getPrimary($site->id);
          if (!$site->site_host) {
            $site = false;
          }
          else {
            $site->site_host->host = 'la' . str_pad($site->id, 4, '0', STR_PAD_LEFT) . '.laklapjap.nl';
            $site->site_host->type = SiteHost::TYPE_SUB;
          }
        }

      }
      else {
        //zoek toch op oude manier
        $site = Site::find_by_host($_SERVER['HTTP_HOST']);
      }

      //hiermee kun je een bepaalde url in een andere template tonen.
      if ($site && in_array($site->site_host->host, ['demo1.laklapjap.nl'])) {
        $_SESSION['DEMOMODUS'] = true;
        if (!(isset($_SESSION['userObject']) && $_SESSION['userObject']->email == '<EMAIL>')) { //deze mag wel bestellen
          $_SESSION['blockorders'] = true;
        }
        if (isset($_GET['template'])) {
          $_SESSION['template'] = $_GET['template'];
        }
        if (!isset($_SESSION['template'])) {
          $_SESSION['template'] = "templateA";
        }
        $site->template = $_SESSION['template'];
      }

    }
    else { //evironment is staging/production

      $site = Site::find_by_host($_SERVER['HTTP_HOST'], true);
      if (!$site) { //niet gevonden. probeer dan zonder www
        $site = Site::find_by_host($_SERVER['HTTP_HOST']);
      }
    }

    if (!$site) {
      if (DEVELOPMENT) {
        include(DIR_ROOT_GSDFW . "modules/site/templates/_site_undefined_dev.php");
        ResponseHelper::exit();
      }

      logToFile("hostmissing", "Site niet gevonden. Onbekende host " . $_SERVER["HTTP_HOST"] . ", ip-adres: " . IpHelper::getIpAdress());
      include(DIR_ROOT_GSDFW . "modules/site/templates/_site_undefined.php");
      ResponseHelper::exit();
    }

    // To comply with gsdfw update 111, compatibility between the class model and the database structure must be ensured. Without this, login functionality and developer tools will not work.
    //    @todo: remove try/catch  when all sites are updated to gsdfw 111.
    try {
      $site->organisation = Organisation::find_by_id($site->organisation_id);
    }
    catch (Exception $e) {
      $site->organisation = Organisation::createOrganisationFromDB($site->organisation_id);
    }

    //allows us to overrule the template on project level, eg when the domain is the same but the template needs to be different (JAM & VDLcontainer)
    if (isset($_SESSION['userObject']) && is_a($_SESSION['userObject'], "User")) {
      $site->overruleTemplate($_SESSION['userObject'], $_SESSION['userObject']->organisation ?? null);
    }

    $_SESSION['site'] = $site; //zet op de sessie
  }
  else {
    $site = $_SESSION['site'];
  }

  Context::setSite($site); //site is a property of the Context

  if (!empty($_GET['lang']) && strlen($_GET['lang']) == 2) {
    $_SESSION['lang'] = $_GET['lang'];
  }
  if (empty($_SESSION['lang'])) {
    //1. kijk naar browser taal 2. pak default taal uit site_host 3. val terug op nederlands
    $det = LanguageHelper::getBrowserLanguage();
    if (!$det && in_array($det, $site->site_host->getPossibleLangs())) {
      $_SESSION['lang'] = $det;
    }
  }
  if (empty($_SESSION['lang']) || (!in_array($_SESSION['lang'], $site->site_host->getPossibleLangs()) && !Config::isTrue("LANGUAGE_IGNORE_CHECK"))) {
    //terugvallen op default en redirect
    if (!empty($site->site_host->default_lang)) {
      $_SESSION['lang'] = $site->site_host->default_lang;
    }
    else {
      $_SESSION['lang'] = 'nl';
    }
    if (!empty($_GET['lang']) && $_GET['lang'] != $_SESSION['lang']) {
      ResponseHelper::redirect($site->site_host->getDomain(true)); //alleen redirect wanneer een taal gezet is die niet actief is voor deze website.
    }
  }

  Trans::loadMainLanguagefile(); //algemene language file

  LanguageHelper::setLocaleByLanguageCode($_SESSION['lang']);

  GsdApplication::initSiteLoaded();
