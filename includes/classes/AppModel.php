<?php

  /**
   * Generic database functions
   *
   * @package    GSDframework
   * <AUTHOR>
   * @copyright  2006-2020
   * @link       https://www.gsd.nl
   */
  class AppModel extends DBConn {

    public bool $from_db = false;
    /** @var array $dirties opmerking: ik kan hier geen array type van maken, aangezien er data kan geserialiseerd zijn */
    private ?array $dirties = []; //check if an object isdirty (alias changed)
    /** @var array $oblidged opmerking: ik kan hier geen array type van maken, aangezien er data kan geserialiseerd zijn */
    private ?array $oblidged = []; //verplichte velden bij formulieren
    private bool $insertIgnore = false; //add IGNORE to insert, which means if a unique column value already exists do no insert but ignore the query. This is usefull if request are fired rapidly. use with caution

    /**
     * add IGNORE to insert, which means if a unique column value already exists do no insert but ignore the query. This is usefull if request are fired rapidly. use with caution
     * @param bool $value
     * @return void
     */
    public function setInsertIgnore(bool $value): void {
      $this->insertIgnore = $value;
    }

    /**
     * is insertIgnore set
     * @return bool
     */
    public function isInsertIgnore(): bool {
      return $this->insertIgnore;
    }

    /**
     * set property of object as dirty.
     * Dirty is a flag that is used by the eventhandlers. If a property is dirty, it means the property is changed.
     *
     * @param string $key name of dirty property
     * @param mixed $oldvalue old value of property
     * @return void
     */
    public function setDirty(string $key, $oldvalue = true): void {
      $this->dirties[$key] = $oldvalue;
    }

    /**
     * Get old value of dirty property
     * @param $key
     * @return bool|mixed
     */
    public function getDirtyOldValue($key): mixed {
      if ($this->hasDirty($key)) {
        return $this->dirties[$key];
      }
      return false;
    }


    /**
     * Get the dirty property names of object
     *
     * @return array|bool dirties
     */
    public function getDirties(): bool|array {
      return empty($this->dirties) ? false : $this->dirties;
    }

    /**
     * Check if a certain property is dirty
     *
     * @param string $key property name
     * @return bool
     */
    public function hasDirty(string $key): bool {
      return isset($this->dirties[$key]);
    }

    /**
     * Unset all dirties
     */
    public function clearDirty(): void {
      $this->dirties = [];
    }

    /**
     * Fill object with values from array [key=>value, key=>value,...]
     * @param $obj_arr []
     */
    public function from_array($obj_arr): void {
      foreach (static::columns as $column) {
        if (array_key_exists($column, $obj_arr)) {
          $this->{$column} = $obj_arr[$column];
        }
      }
    }

    /**
     * Gets object from database and compares with current object.
     * Functions retuns key=>array(old , new)
     */
    public function getChanged($primaryKey = 'id'): array {
      if ($this->$primaryKey == null) return []; //nieuw object heeft geen changes welke we willen houden
      $values = [];
      $db_object = self::generic_find_by([$primaryKey => $this->$primaryKey], static::OM_CLASS_NAME);
      foreach (static::columns as $cn) {
        if ($cn == 'updateTS' || $cn == 'updateUser') continue;
        if ($db_object->{$cn} != $this->{$cn}) {
          //pd("[user ".$cn."] verandert van [".$db_object->{$cn}."] naar [".$this->{$cn}."]");
          $values[$cn]['old'] = $db_object->{$cn};
          $values[$cn]['new'] = $this->{$cn};
        }
      }
      return $values;
    }

    /**
     * @param array|null $conditions
     * @param string $klass
     * @param string $raw_sql
     * @return int|[]
     * @throws GsdDbException
     * @throws GsdException
     */
    protected static function generic_count_all_by(?array $conditions, string $klass, string $raw_sql = '') {
      if (empty($klass)) {
        throw new GsdException("AppModel generic_count_all_by no class defined.");
      }

      $query = "SELECT count(*) AS count FROM " . static::getTablename();

      $where = '';

      if (!empty($conditions)) {
        $where .= 'WHERE ';

        foreach ($conditions as $column => $value) {
          if (in_array($column, static::columns)) {
            if (is_array($value)) {
              if (count($value) == 0) {
                $where .= '0'; //empty, so select zero items
              }
              else {
                $where .= "`$column` IN (";
                foreach ($value as $subval) {
                  $where .= "'" . static::db_link()->escape_string($subval) . "',";
                }
                $where = substr($where, 0, strlen($where) - 1);
                $where .= ')';
              }
            }
            else {
              if ($value === null) {
                $where .= "`$column` IS NULL ";
              }
              else {
                $where .= "`$column` = '" . static::db_link()->escape_string($value) . "'";
              }
            }
            $where .= ' AND ';
          }
          else {
            throw new GsdDbException("DB error: unknown column: $column");
          }
        }
        // cut off last AND
        $where = substr($where, 0, strlen($where) - 5);
      }
      $query = "$query $where $raw_sql";

      if ($result = static::db_link(self::getDatabaseName())->query($query)) {
        if ($result->num_rows == 1) {
          $row = $result->fetch_row();

          return (int)$row[0];
        }
        // support for multiple counts when using GROUP BY
        elseif ($result->num_rows > 1) {
          //vanwege deze return, kan ik de return type niet int maken
          return $result->num_rows;
        }
        return 0;
      }
      static::db_link()->handleError($query);
    }

    /**
     * @param array|null $conditions
     * @param string $klass
     * @param string $raw_sql
     * @return array
     * @throws GsdDbException
     * @throws GsdException
     */
    protected static function generic_find_all_like(?array $conditions, string $klass, string $raw_sql = ''): array {
      if (empty($klass)) {
        throw new GsdException("AppModel generic_find_all_like no class defined.");
      }
      if (empty($conditions)) {
        return [];
      }

      $query = 'SELECT ' . implode(', ', static::columns) . " FROM " . static::getTablename();

      $where = 'WHERE ';

      foreach ($conditions as $column => $value) {
        if (in_array($column, static::columns)) {
          $where .= "`$column` LIKE '" . static::db_link()->escape_string($value) . "' AND ";
        }
        else {
          throw new GsdDbException("DB error: unknown column: $column");
        }
      }


      // cut off last AND
      $where = substr($where, 0, strlen($where) - 5);
      $query = "$query $where $raw_sql";

      if ($result = static::db_link(self::getDatabaseName())->query($query)) {
        $all = [];
        if ($result->num_rows > 0) {
          while ($row = $result->fetch_assoc()) {
            /** @var AppModel $obj */
            $obj = new $klass;
            $obj->from_db = true;

            foreach ($row as $column => $value) {
              $obj->{$column} = $value;
            }
            $obj->update();
            $all[] = $obj;
          }
        }

        return $all;
      }

      static::db_link()->handleError($query);
    }

    /**
     * @param array|null $conditions
     * @param string $klass
     * @param string $raw_sql
     * @return array
     * @throws GsdDbException
     * @throws GsdException
     */
    protected static function generic_find_all_by(?array $conditions, string $klass, string $raw_sql = ''): array {
      if (empty($klass)) {
        throw new GsdException("AppModel generic_find_all_by no class defined.");
      }

      $query = 'SELECT `' . static::TABLE_NAME . '`.`' . implode('`, `' . static::TABLE_NAME . '`.`', static::columns) . "` FROM " . static::getTablename();

      $where = '';

      if (!empty($conditions)) {
        $where .= 'WHERE ';

        foreach ($conditions as $column => $value) {
          if (in_array($column, static::columns)) {
            if (is_array($value)) {
              if (count($value) == 0) {
                $where .= '0'; //empty, so select zero items
              }
              else {
                $where .= "`$column` IN (";
                foreach ($value as $subval) {
                  if (empty($subval)) {
                    $where .= "'',";
                  }
                  else {
                    $where .= "'" . static::db_link()->escape_string($subval) . "',";
                  }
                }
                $where = substr($where, 0, strlen($where) - 1);
                $where .= ')';
              }
            }
            else {
              if ($value === null) {
                $where .= "`$column` IS NULL ";
              }
              else {
                $where .= "`$column` = '" . static::db_link()->escape_string($value) . "'";
              }
            }
            $where .= ' AND ';
          }
          else {
            throw new GsdDbException("DB error: unknown column: $column");
          }
        }
        // cut off last AND
        $where = substr($where, 0, strlen($where) - 5);
      }
      $query = "$query $where $raw_sql";

      if ($result = static::db_link(self::getDatabaseName())->query($query)) {
        $all = [];
        if ($result->num_rows > 0) {
          while ($row = $result->fetch_assoc()) {
            $obj = new $klass;
            $obj->from_db = true;
            foreach ($row as $column => $value) {
              $obj->{$column} = $value;
            }
            $obj->update();
            $all[] = $obj;
          }
        }

        return $all;
      }

      static::db_link()->handleError($query);

    }

    /**
     * @param string $klass
     * @param string $raw_sql
     * @return array
     * @throws GsdDbException
     * @throws GsdException
     */
    protected static function generic_find_all(string $klass, string $raw_sql = ''): array {
      return self::generic_find_all_by([], $klass, $raw_sql);
    }

    /**
     * @param array|null $conditions
     * @param string $klass
     * @param string $raw_sql
     * @return false|mixed
     * @throws GsdDbException
     * @throws GsdException
     */
    protected static function generic_find_by(?array $conditions, string $klass, string $raw_sql = '') {

      if ($raw_sql == '' || strpos($raw_sql, 'LIMIT') === false) {
        $raw_sql .= ' LIMIT 1';
      }
      $result = self::generic_find_all_by($conditions, $klass, $raw_sql);

      if ($result && count($result) > 0) {
        return $result[0];
      }

      return false;
    }

    /**
     * @param null|int|string $id
     * @param string $klass
     * @param string $raw_sql
     * @return array|false|mixed|string
     * @throws GsdDbException
     * @throws GsdException
     */
    protected static function generic_find_by_id($id, string $klass, string $raw_sql = '') {
      if ($raw_sql == "" && $obj = self::getCacheById($id)) {
        return $obj;
      }

      $result = self::generic_find_all_by(['id' => $id], $klass, $raw_sql);

      if ($result && count($result) > 0) {
        if ($raw_sql == "") {
          self::addCacheById($id, $result[0]);
        }

        return $result[0];
      }

      return false;
    }

    /**
     * @param array $conditions
     * @param string $klass
     * @return mixed
     * @throws GsdDbException
     * @throws GsdException
     */
    protected static function generic_find_or_create_by(array $conditions, string $klass) {
      $result = self::generic_find_all_by($conditions, $klass);
      if ($result && count($result) > 0) {
        return $result[0];
      }

      $obj = new $klass;
      $obj->from_array($conditions);

      return $obj;
    }

    /**
     * Saves object to database
     *
     * Make sure this object get's saved back to the database with it's current values.
     * Before save, we call the object's function valid_required_fields() which should return true to proceed.
     * If the object has a non-empty primary key then update else insert.
     * @param array $errors
     * @return bool|mysqli_result
     * @throws GsdDbException
     * @throws GsdException
     * @throws Exception
     */
    public function save(array &$errors = []) {
      $result = false;

      if ($this->valid_required_fields($errors)) {
        // get model specific columns
        $pri_key_columns = static::$primary_key;

        $primary_key = [];
        $columns = [];

        // sql escape all values that are set
        // and determine primary key
        foreach (static::columns as $column_name) {
          if (property_exists($this, $column_name)) {
            if (is_null($this->{$column_name})) {
              $columns[$column_name] = 'NULL';
            }
            elseif (is_float($this->{$column_name})) {
              $columns[$column_name] = str_replace(',', '.', $this->{$column_name});
            }
            else {
              $columns[$column_name] = "'" . static::db_link()->escape_string($this->{$column_name}) . "'";
            }

            if (in_array($column_name, $pri_key_columns)) {
              $primary_key[$column_name] = $columns[$column_name];
            }
          }
        }

        if ($this->from_db) {
          $sql_set = '';
          foreach ($columns as $col_name => $col_value) {
            $sql_set .= ", " . static::TABLE_NAME . "." . $col_name . " = " . $col_value;
          }
          // skip the first ', ' from the query
          $sql_set = substr($sql_set, 2);
          $sql_where = '';

          foreach ($primary_key as $col_name => $col_value) {
            $sql_where .= " AND $col_name = $col_value";
          }
          // skip the first ' AND ' from the query
          $sql_where = substr($sql_where, 5);
          $query = "UPDATE " . static::getTablename() . " SET $sql_set WHERE $sql_where";

          $result = static::db_link(self::getDatabaseName())->query($query);
        }
        else {
          $query = "INSERT ";
          if ($this->isInsertIgnore()) {
            $query .= "IGNORE "; //high performance query, IGNORE query if unique column value already exists
          }
          $query .= "INTO " . static::getTablename() . " (" .  static::getTablename() . '.' . implode(', ' . static::getTablename() . '.', array_keys($columns)) . ") VALUES(" . implode(',', $columns) . ")";

          if ($result = static::db_link(self::getDatabaseName())->query($query)) {
            $this->from_db = true;
            // if the table had an auto_increment field, use it to update it (this implies that the primary key is 1 column)
            if ($id = static::db_link(self::getDatabaseName())->insert_id) {
              $this->{$pri_key_columns[0]} = $id;
            }
          }
        }
        if (!$result) {
          $errors[] = static::db_link()->error;
          static::db_link()->handleError($query);
        }

        //naar cache
        self::addCacheById($this->{$pri_key_columns[0]}, $this);

        $this->update();
        $this->clearDirty();
      }

      if (!$result) {
        //opslaan van een object wat niet valide is...
        $msg = "INVALID MODEL SAVE\n";
        $msg .= "PROJECT: " . PROJECT . "\n";
        $msg .= 'URL: <a href="https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . '">' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . "</a>\n";
        if (isset($_SESSION['userObject']) && isset($_SESSION['userObject']->id)) $msg .= "UID: " . $_SESSION['userObject']->id . "\n";
        $msg .= "ERRORS: \n";
        foreach ($errors as $error) {
          $errorval = "'" . $this->{$error} . "'";
          if ($this->{$error} == null) {
            $errorval = "null";
          }
          elseif ($this->{$error} == "") {
            $errorval = "empty string";
          }
          $msg .= "- " . $error . ": value=" . $errorval;
          $fs = $this->getFieldstructureFormatted($error);
          if ($fs != "") {
            $msg .= " [fieldstructure: " . $fs . "]";
          }
          $msg .= "\n";
        }
        if (MAIL_ALL_ERRORS) { //on local develoment, do not show trace. This is already shown in tracy
          $trace = (new GsdException)->getExceptionTraceAsString();
          $msg .= "BACKTRACE:\n" . $trace;
        }
        $msg .= "OBJECT:\n" . print_r($this, true) . "\n";

        logToFile("object_invalid", $msg);
        //$msg mag maximaal 1024 bytes lang zijn, anders word deze truncated door trigger_error
        throw new GsdDbException($msg);
      }

      return $result;
    }

    /**
     * Delete object from database
     *
     * @return bool|mysqli_result
     * @throws GsdDbException
     * @throws GsdException
     * @throws Exception
     */
    public function destroy() {
      $result = false;
      $query = '';
      if ($this->from_db) {
        $pri_key_columns = static::$primary_key;

        $sql_where = '';
        // sql escape the primary key
        foreach ($pri_key_columns as $column_name) {
          $sql_where .= " AND $column_name = '" . static::db_link()->escape_string($this->{$column_name}) . "'";
        }
        // skip the first ' AND ' from the query
        $sql_where = substr($sql_where, 5);

        $query = "DELETE FROM " . static::getTablename() . " WHERE $sql_where";

        $result = static::db_link(self::getDatabaseName())->query($query);
      }
      if (!$result) {
        static::db_link()->handleError($query);
      }
      $this->flushCache();

      return $result;
    }

    /**
     * Check if table of object exists.
     * @return bool
     */
    public static function tableExists() {
      //do not use getTablename(), we need table without databasename added.
      $result = static::db_link(self::getDatabaseName())->query("SHOW TABLES LIKE '" . static::TABLE_NAME . "'");
      if ($result->num_rows == 1) {
        return true;
      }
      return false;
    }

    public function update() {
      return true;
    }

    /**
     * @param array $error_codes
     * @return bool
     */
    public function valid(&$error_codes = []) {
      return $this->valid_required_fields($error_codes);
    }

    /**
     * @param array $error_codes
     * @return bool
     */
    public function valid_required_fields(&$error_codes = []) {
      $errors = [];
      foreach (static::columns as $column) {
        if ($column != $this->auto_increment) {
          if (!$this->validateProperty($column)) {
            $errors[] = $column;
          }
        }
      }
      if (empty($errors)) {
        return true;
      }
      $error_codes = array_merge($error_codes, $errors);
      return false;
    }

    /**
     * @param string $property
     * @return bool
     */
    public function validateProperty(string $property): bool {
      $property_structure = $this::field_structure[$property];
      $value = $this->{$property};
      $type = $property_structure["type"];
      $length = $property_structure["length"];
      $maybe_null = $property_structure["null"];
      $supported_types = [
        'float',
        'double',
        'decimal',
        'enum',
        'int',
        'tinyint',
        'bigint',
        'mediumint',
        'smallint',
        'datetime',
        'timestamp',
        'time',
        'date',
        'char',
        'text',
        'mediumtext',
        'longtext',
        'varchar',
        'boolean',
      ];

      if (!in_array($type, $supported_types)) {
        return true;
      }

      if (is_null($value) || strlen($value) == 0) { //hij is null of leeg, dan alleen kijken of dat mag
        return $maybe_null;
      }
//      pd($type." = ".$value);
      switch ($type) {
        case 'varchar':
          return self::valid_varchar($value, $length);
        case 'char':
          return self::valid_char($value, $length);
        case 'text':
          return self::valid_text($value);
        case 'mediumtext':
          return self::valid_mediumtext($value);
        case 'longtext':
          return self::valid_longtext($value);
        case 'tinyint':
          return self::valid_tinyint($value);
        case 'smallint':
          return self::valid_smallint($value);
        case 'mediumint':
          return self::valid_mediumint($value);
        case 'int':
          return self::valid_int($value);
        case 'bigint':
          return self::valid_bigint($value);
        case 'decimal':
          return self::valid_decimal($value, $length);
        case 'float':
          return self::valid_float($value, $length);
        case 'double':
          return self::valid_double($value, $length);
        case 'enum':
          return self::valid_enum($value, $property_structure["enums"]);
        case 'datetime':
          return self::valid_datetime($value);
        case 'timestamp':
          return self::valid_timestamp($value);
        case 'date':
          return self::valid_date($value);
        case 'time':
          return self::valid_time($value);
        case 'boolean':
          return self::valid_boolean($value);
      }
      return true;
    }

    public static function valid_varchar($input, $max_length): bool {
      //we gebruiken mb_strlen zodat ook aantal karakters utf8 (standaard voor tekstvelden in de db is utf8_general_ci) goed geteld worden
      if (mb_strlen($input) <= $max_length) {
        return true;
      }

      return false;
    }

    public static function valid_char($input, $max_length): bool {
      if (mb_strlen($input) <= $max_length) {
        return true;
      }

      return false;
    }

    public static function valid_date($input): bool {
      $exploded = explode('-', $input);

      $year = $exploded[0] ?? null;
      $month = $exploded[1] ?? null;
      $day = $exploded[2] ?? null;

      if (!is_numeric($year) || !is_numeric($month) || !is_numeric($day)) {
        return false;
      }

      return checkdate($month, $day, $year);
    }

    public static function valid_time($input): bool {
      $vals = explode(':', $input);
      if (count($vals) == 2)
        $vals[] = '00';
      [$hours, $minutes, $seconds] = $vals;

      if ((intval($hours) == $hours) && (intval($minutes) == $minutes) && (intval($seconds) == $seconds) && ($hours <= 24 && $hours >= 0) && ($minutes <= 59 && $minutes >= 0) && ($seconds <= 59 && $seconds >= 0)) {
        return true;
      }

      return false;
    }

    public static function valid_float($input, $length_and_decimal): bool {
      $input_integer = $input;
      $input_fraction = 0;
      $allowed_integer = $length_and_decimal;
      $allowed_fraction = 0;
      if (strpos($input, ",") !== false) {
        [$input_integer, $input_fraction] = explode(',', $input);
      }
      elseif (strpos($input, ".") !== false) {
        [$input_integer, $input_fraction] = explode('.', $input);
      }

      if (strpos($length_and_decimal, ',')) {
        [$allowed_integer, $allowed_fraction] = explode(',', $length_and_decimal);
      }

      if (floatval($input) == $input && strlen($input_integer) <= $allowed_integer) {
        return true;
      }

      return false;
    }

    public static function valid_datetime($input): bool {
      $vals = explode(' ', $input);
      $date = $vals[0];
      if (isset($vals[1])) {
        $time = $vals[1];
      }
      if (self::valid_date($date) && (!isset($time) || self::valid_time($time))) {
        return true;
      }

      return false;
    }

    public static function valid_timestamp($input): bool {
      return self::valid_datetime($input);
    }

    public static function valid_boolean($input): bool {
      if ($input === false || $input === true || $input == 0 || $input == 1) {
        return true;
      }
      return false;
    }

    /**
     * Valideer tinyint
     * I.v.m. backwards compatibility moet deze functie ook boolean false en true goed vinden.
     * Ik zie dat in de code soms true en false word gebruikt voor een tinyint property ipv 1 en 0.
     * Bij het saven zorgt de appmodel dat deze code netjes als 1 en 0 word opgeslagen in de datbase,
     * maar strict gezien is dat geen juist gebruikt van tinyint.
     * @param      $input
     * @param bool $maxlength : deprecated, do not use. The number when creating an int in mysql doesn't say anything about the max size of an int
     * @return bool
     */
    public static function valid_tinyint($input, $maxlength = false): bool {
      //max value unsigned: 255. Use unsigned value = max value for backwards compatibility
      //max value signed: 127
      if (self::valid_boolean($input) || (StringHelper::isInt($input) && intval($input) <= 255)) {
        return true;
      }
      return false;
    }

    public static function valid_smallint($input, $maxlength = false): bool {
      //max value unsigned: 65535. Use unsigned value = max value for backwards compatibility
      //max value signed: 32767
      if (StringHelper::isInt($input) && intval($input) <= 65535) {
        return true;
      }
      return false;
    }

    /**
     * @param $input
     * @param $max_length : deprecated, do not use. The number when creating an int doesn't say anything about the max size of an int
     * @return bool
     */
    public static function valid_int($input, $max_length = false): bool {
      //max value unsigned: 4294967295. Use unsigned value = max value for backwards compatibility
      //max value signed: 2147483647
      if (StringHelper::isInt($input) && intval($input) <= 4294967295) {
        return true;
      }
      return false;
    }

    public static function valid_mediumint($input, $maxlength = false): bool {
      //max value unsigned: 16777215. Use unsigned value = max value for backwards compatibility
      //max value signed: 8388607
      if (StringHelper::isInt($input) <= 16777215) {
        return true;
      }
      return false;
    }

    public static function valid_bigint($input, $maxlength = false): bool {
      //max value unsigned: 2^64-1. Use unsigned value = max value for backwards compatibility
      //max value signed: 2^63-1
      if (StringHelper::isInt($input) && intval($input) <= 2 ^ 64 - 1) {
        return true;
      }
      return false;
    }


    public static function valid_decimal($input, $length_and_decimal): bool {
      if (is_numeric($input)) {
        return true;
      }
      return true;
    }

    public static function valid_enum($input, $possible_values): bool {
      return in_array($input, $possible_values);
    }

    public static function valid_double($input, $length_and_decimal): bool {
      return self::valid_float($input, $length_and_decimal);
    }

    public static function valid_text($input): bool {
      return self::valid_varchar($input, pow(2, 16) - 1);
    }

    public static function valid_mediumtext($input): bool {
      return self::valid_varchar($input, pow(2, 24) - 1);
    }

    public static function valid_longtext($input): bool {
      return self::valid_varchar($input, pow(2, 32) - 1);
    }

    public function hydrate($data, $offset = 0) {
      for ($i = 0; $i < count(static::columns); $i++) {
        $this->{static::columns[$i]} = $data[$i + $offset];
      }
    }


    /**
     * Updated version of hydrate for easy oneliners, when hydrating multiple models.
     * Use as:
     * $column_counter  = 0;
     * $product         = (new Product())->hydrateNext($row, $column_counter);
     * $product_content = (new ProductContent())->hydrateNext($row, $column_counter);
     * Objects have from_db = true
     *
     * @param     $data
     * @param int $offset
     * @return $this
     */
    public function hydrateNext($data, &$offset = 0) {
      for ($i = 0; $i < count(static::columns); $i++) {
        $this->{static::columns[$i]} = $data[$i + $offset];
      }
      $this->from_db = true; //these are objects from the db and can be saved.
      $offset += count(static::columns);
      return $this;
    }

    /**
     * Get object variables as array
     * @return array
     */
    public function asArray(): array {
      return get_object_vars($this);
    }

    /**
     * Create an array width key property of the object array item as key
     * You can also use array_column($arrayin, null, 'id'));
     *
     * @param array|null $arrayin
     * @param string $key default 'id'
     * @return array
     */
    public static function mapObjectIds(?array $arrayin, string $key = 'id'): array {
      if (empty($arrayin)) return [];
      $newarray = [];
      foreach ($arrayin as $obj) {
        $newarray[$obj->{$key}] = $obj;
      }
      return $newarray;
    }

    /**
     * Strip Appmodel classes of unnecessary properties. This is handy for use in json.
     *
     * @param       $object : a model class
     * @param array $preserve : preserve properties that are added, and are not standard to the basemodel class
     * @param bool $ignore_null : remove properties with null value
     * @return stdClass
     */
    public static function plainObject($object, array $preserve = [], bool $ignore_null = false) {
      if (!is_object($object)) { //geen object, gewoon teruggeven
        return $object;
      }
      $new = new stdClass();
      $class_name = get_class($object);
      if (!defined("$class_name::columns")) { //dit is geen AppModel object, gewoon teruggeven
        return $object;
      }
      foreach ($object::columns as $column) {
        if (!$ignore_null || $object->{$column} != null) {
          $new->{$column} = $object->{$column};
        }
      }
      foreach ($preserve as $keep) {
        if (property_exists($object, $keep)) {
          if (is_object($object->{$keep})) {
            $new->{$keep} = AppModel::plainObject($object->{$keep}, $preserve, $ignore_null);
          }
          elseif (is_array($object->{$keep})) {
            $new->{$keep} = AppModel::plainObjects($object->{$keep}, $preserve, $ignore_null);
          }
          else {
            $new->{$keep} = $object->{$keep};
          }
        }
      }

      return $new;
    }

    /**
     * Strip array of Appmodel classes of unnecessary properties. This is handy for use in json.
     *
     * @param array $objects : array with models
     * @param array $preserve : preserve properties that are added, and are not standard to the basemodel class
     * @param bool $ignore_null : remove properties with null value
     * @return array
     */
    public static function plainObjects(array $objects, array $preserve = [], bool $ignore_null = false): array {
      foreach ($objects as $k => $object) {
        $objects[$k] = AppModel::plainObject($object, $preserve, $ignore_null);
      }

      return $objects;
    }

    /**
     * @param array|null $conditions
     * @param string $klass
     * @param string $raw_sql
     * @return bool
     * @throws GsdDbException
     * @throws GsdException
     */
    protected static function generic_delete_by(?array $conditions, string $klass, string $raw_sql = ''): bool {
      if (empty($klass)) {
        throw new GsdException("AppModel generic_delete_by no class defined.");
      }

      $query = "DELETE FROM " . static::getTablename();

      $where = '';

      if (!empty($conditions)) {
        $where .= 'WHERE ';

        foreach ($conditions as $column => $value) {
          if (in_array($column, static::columns)) {
            if (is_array($value)) {
              if (count($value) == 0) {
                $where .= '0'; //empty, so select zero items
              }
              else {
                $where .= "`$column` IN (";
                foreach ($value as $subval) {
                  $where .= "'" . static::db_link()->escape_string($subval) . "',";
                }
                $where = substr($where, 0, strlen($where) - 1);
                $where .= ')';
              }
            }
            else {
              $where .= "`$column` = '" . static::db_link()->escape_string($value) . "'";
            }
            $where .= ' AND ';
          }
          else {
            throw new GsdDbException("DB error: unknown column: $column");
          }
        }
        // cut off last AND
        $where = substr($where, 0, strlen($where) - 5);
      }
      $query = "$query $where $raw_sql";

      if (Config::isTrue("CACHE_OM_ENABLED")) {
        //omdat memcache geen wildcard flush heeft moeten we hier eerst de id's ophalen welke verwijderd worden.
        $pri_key_columns = static::$primary_key;
        $delquery = "SELECT " . $pri_key_columns[0] . " FROM " . static::getTablename() . " ";
        $delquery .= "$where $raw_sql";
        if ($result = static::db_link(self::getDatabaseName())->query($delquery)) {
          $cachekey = self::getCacheKey();
          while ($row = $result->fetch_row()) {
            self::flushCacheByKey($cachekey . $row[0]);
          }
        }
      }

      if (static::db_link(self::getDatabaseName())->query($query)) {
        return true;
      }
      static::db_link()->handleError($query);

    }

    //------------------------CACHE FUNCTIONS------------------------

    /**
     * @param $id
     * @param $data
     * @return void
     */
    public static function addCacheById($id, $data): void {
      if (!Config::isTrue("CACHE_OM_ENABLED")) return;
      CacheManager::setData(self::getCacheKey() . $id, $data);
    }

    /**
     * Get cache by database id. Helper function
     */
    public static function getCacheById($id) {
      if (!Config::isTrue("CACHE_OM_ENABLED"))
        return null;

      return CacheManager::getData(self::getCacheKey() . $id);
    }

    /**
     * Flush this object from cache
     */
    public function flushCache() {
      if (!Config::isTrue("CACHE_OM_ENABLED"))
        return null;
      $pri_key_columns = static::$primary_key;

      return self::flushCacheByKey(self::getCacheKey() . $pri_key_columns[0]);
    }

    /**
     * Flush on object with certain key from cache memory
     *
     * @param string $id
     * @return bool|null
     */
    public static function flushCacheByKey($key) {
      if (!Config::isTrue("CACHE_OM_ENABLED"))
        return null;

      return CacheManager::delData($key);
    }

    /**
     * Flush object with db id
     *
     * @param string $id
     * @return bool|null
     */
    public static function flushCacheById($id) {
      if (!Config::isTrue("CACHE_OM_ENABLED"))
        return null;

      return self::flushCacheByKey(self::getCacheKey() . $id);
    }

    /**
     * Get Cache key
     *
     * @return string
     */
    public static function getCacheKey() {
      return PROJECT . '-' . static::TABLE_NAME . '-';
    }

    /**
     *  Get this object Cache key
     */
    public function getCacheKeyObject() {
      $pri_key_columns = static::$primary_key;

      return self::getCacheKey() . $pri_key_columns[0];
    }

    /**
     * Load model class, first look in project folder, then in gsdfw
     * @param string $classname
     * @return bool
     * @throws Exception
     */
    public static function loadModelClass(string $classname): bool {
      if (file_exists(DIR_PROJECT_FOLDER . 'classes/cm/' . $classname . '.php')) {
        require_once(DIR_PROJECT_FOLDER . 'classes/cm/' . $classname . '.php');

        return true;
      }
      if (Config::isdefined("GSDFW_PROJECT_EXTENDS") && Config::get("GSDFW_PROJECT_EXTENDS") != "" && file_exists(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/classes/cm/' . $classname . '.php')) {
        require_once(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/classes/cm/' . $classname . '.php');

        return true;
      }
      if (defined('DIR_PLUGIN_FOLDER') && Config::isdefined("GSDFW_PLUGINS")) {
        foreach (Config::get("GSDFW_PLUGINS") as $plugin) {
          if (file_exists(DIR_PLUGIN_FOLDER . $plugin . '/classes/cm/' . $classname . '.php')) {
            require_once(DIR_PLUGIN_FOLDER . $plugin . '/classes/cm/' . $classname . '.php');
            return true;
          }
        }
      }
      if (file_exists(DIR_CLASSES . 'cm/' . $classname . '.php')) {
        require_once(DIR_CLASSES . 'cm/' . $classname . '.php');

        return true;
      }

      return false;
    }

    /**
     * Load base class, first look in project folder, then in gsdfw
     * @param string $classname
     * @return bool
     * @throws Exception
     */
    public static function loadBaseClass(string $classname): bool {
      if (file_exists(DIR_PROJECT_FOLDER . 'classes/bm/' . $classname . '.php')) {
        require_once(DIR_PROJECT_FOLDER . 'classes/bm/' . $classname . '.php');

        return true;
      }
      if (Config::isdefined("GSDFW_PROJECT_EXTENDS") && Config::get("GSDFW_PROJECT_EXTENDS") != "" && file_exists(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/classes/bm/' . $classname . '.php')) {
        require_once(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/classes/bm/' . $classname . '.php');

        return true;
      }
      if (defined('DIR_PLUGIN_FOLDER') && Config::isdefined("GSDFW_PLUGINS")) {
        foreach (Config::get("GSDFW_PLUGINS") as $plugin) {
          if (file_exists(DIR_PLUGIN_FOLDER . $plugin . '/classes/bm/' . $classname . '.php')) {
            require_once(DIR_PLUGIN_FOLDER . $plugin . '/classes/bm/' . $classname . '.php');

            return true;
          }
        }
      }
      if (file_exists(DIR_CLASSES . 'bm/' . $classname . '.php')) {
        require_once(DIR_CLASSES . 'bm/' . $classname . '.php');

        return true;
      }

      return false;
    }

    /**
     * Load project model class, first look in project folder, then in other project folder
     * @param string $classname
     * @return void
     * @throws Exception
     */
    public static function loadProjectModelClass(string $classname): void {
      if (file_exists(DIR_PROJECT_FOLDER . 'classes/pm/' . $classname . '.php')) {
        require_once(DIR_PROJECT_FOLDER . 'classes/pm/' . $classname . '.php');
      }
      elseif (Config::isdefined("GSDFW_PROJECT_EXTENDS") && Config::get("GSDFW_PROJECT_EXTENDS") != "" && file_exists(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/classes/pm/' . $classname . '.php')) {
        require_once(DIR_ROOT . 'projects/' . strtolower(Config::get("GSDFW_PROJECT_EXTENDS")) . '/classes/pm/' . $classname . '.php');
      }
    }

    /**
     * Is key oblidged
     * @param string $key
     * @return bool
     */
    public function isOblidged(string $key): bool {
      if (empty($this->oblidged)) return false;
      return in_array($key, $this->oblidged);
    }

    /**
     * Add to oblidged
     * @param string $key
     * @return void
     */
    public function setOblidged(string $key): void {
      if ($this->oblidged == null) $this->oblidged = []; //kan niet weg ivm geserialiseerde modellen
      if (!in_array($key, $this->oblidged)) {
        $this->oblidged[] = $key;
      }
    }

    /**
     * Remove from oblidged array
     * @param $key
     * @return void
     */
    public function removeOblidged($key): void {
      if (empty($this->oblidged)) return;
      $akey = array_search($key, $this->oblidged);
      if ($akey !== false) {
        unset($this->oblidged[$akey]);
      }
    }

    /**
     * Set oblidged array
     * @param array $ar
     * @return void
     */
    public function setOblidgedArray(array $ar): void {
      $this->oblidged = $ar;
    }

    public function getFieldstructureFormatted($property): string {
      if (defined(get_class($this) . "::field_structure")) {
        $str = [];
        foreach ($this::field_structure[$property] as $key => $value) {
          $propvalue = $value;
          if (is_array($value)) {
            $propvalue = implode(",", $value);
          }
          elseif (is_bool($value)) {
            $propvalue = $value ? 'true' : 'false';
          }
          $str[] = $key . "=" . $propvalue;
        }
        return implode(", ", $str);
      }

      return '';
    }

    /**
     * Get full table name including databasename for building queries.
     * (else we can get .TABLE_NAME, which gives problems in mysql 8)
     * @return string
     * @throws Exception
     */
    public static function getTablename(): string {
      if (self::getDatabaseName() != "") {
        return self::getDatabaseName() . "." . static::TABLE_NAME;
      }
      return static::TABLE_NAME;
    }

    /**
     * Get database name of current model
     * @return string
     * @throws Exception
     */
    public static function getDatabaseName(): string {
      if (static::DB_NAME != "") { //databasename from model
        if (Config::isdefined("DATABASENAME_REPLACE") && isset(Config::get("DATABASENAME_REPLACE")[static::DB_NAME])) {
          return Config::get("DATABASENAME_REPLACE")[static::DB_NAME];
        }
        return static::DB_NAME;
      }
      return DB_NAME;//database name from configure_root
    }

  }